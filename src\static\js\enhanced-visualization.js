// Enhanced Visualization with Molecular Structures in Nodes
let enhancedCy; // Enhanced Cytoscape instance
let smilesDrawer; // SmilesDrawer instance

$(document).ready(function() {
    console.log("Enhanced visualization loaded");
    
    // Initialize SmilesDrawer
    if (typeof SmilesDrawer !== 'undefined') {
        smilesDrawer = new SmilesDrawer.Drawer({
            width: 150,
            height: 150,
            bondThickness: 2,
            bondLength: 15,
            shortBondLength: 0.85,
            bondSpacing: 0.18 * 15,
            atomVisualization: 'default',
            isomeric: true,
            debug: false,
            terminalCarbons: false,
            explicitHydrogens: false,
            overlapSensitivity: 0.42,
            overlapResolutionIterations: 1,
            compactDrawing: true,
            fontSizeLarge: 11,
            fontSizeSmall: 9,
            padding: 10.0,
            experimentalSSSR: true,
            kkThreshold: 0.1,
            kkInnerThreshold: 0.1,
            kkMaxIteration: 20000,
            kkMaxInnerIteration: 50,
            kkMaxEnergy: 1e9
        });
    }
});

// Create enhanced visualization with molecular structures
function createEnhancedVisualization(dataId, threshold = 10) {
    console.log("Creating enhanced visualization for data ID:", dataId);
    
    // Show loading indicator
    $('#loading-indicator').removeClass('d-none');
    $('#error-message').addClass('d-none');
    
    // Create hierarchical visualization first
    $.ajax({
        url: '/api/visualization/hierarchical',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            data_id: dataId,
            threshold: threshold
        }),
        success: function(response) {
            console.log("Hierarchical visualization created:", response);
            if (response.success) {
                // Load the visualization data
                loadEnhancedVisualization(response.data_id);
            } else {
                showError(`Failed to create visualization: ${response.error}`);
            }
        },
        error: function(xhr, status, error) {
            console.error("Visualization creation error:", error);
            showError(`Error creating visualization: ${error}`);
        }
    });
}

// Load enhanced visualization data
function loadEnhancedVisualization(vizId) {
    $.ajax({
        url: `/api/visualization/visualization/${vizId}`,
        type: 'GET',
        success: function(response) {
            console.log("Enhanced visualization data loaded:", response);
            if (response.success) {
                const data = response.data;
                const visualization = data.visualization;
                
                // Initialize enhanced Cytoscape
                initEnhancedCytoscape(visualization);
                
                // Hide loading indicator
                $('#loading-indicator').addClass('d-none');
            } else {
                showError(`Failed to load visualization: ${response.error}`);
            }
        },
        error: function(xhr, status, error) {
            console.error("Visualization loading error:", error);
            showError(`Error loading visualization: ${error}`);
        }
    });
}

// Initialize enhanced Cytoscape with molecular structures
function initEnhancedCytoscape(visualization) {
    const nodes = visualization.nodes || [];
    const edges = visualization.edges || [];
    
    console.log(`Initializing enhanced Cytoscape with ${nodes.length} nodes and ${edges.length} edges`);
    
    // Clear existing visualization
    if (enhancedCy) {
        enhancedCy.destroy();
    }
    
    // Prepare elements with enhanced node data
    const elements = {
        nodes: nodes.filter(node => node.visible).map(node => ({
            data: {
                id: node.id,
                type: node.type,
                name: node.name || node.id,
                smiles: node.smiles || '',
                svg: node.svg || '',
                activity: node.activity || 0,
                activity_unit: node.activity_unit || 'μM',
                pIC50: node.pIC50 || 0,
                parent: node.parent || null,
                expanded: node.expanded || false,
                has_children: node.has_children || false,
                lipinski_violations: node.lipinski_violations || 0,
                count: node.count || 0
            },
            classes: node.type
        })),
        edges: edges.filter(edge => edge.visible).map(edge => ({
            data: {
                source: edge.source,
                target: edge.target
            }
        }))
    };
    
    // Initialize enhanced Cytoscape
    enhancedCy = cytoscape({
        container: document.getElementById('cy'),
        elements: elements,
        style: getEnhancedNodeStyles(),
        layout: getEnhancedLayout()
    });
    
    // Add event listeners
    enhancedCy.on('tap', 'node', function(evt) {
        const node = evt.target;
        showEnhancedNodeDetails(node);
    });
    
    enhancedCy.on('mouseover', 'node', function(evt) {
        const node = evt.target;
        highlightNode(node);
    });
    
    enhancedCy.on('mouseout', 'node', function(evt) {
        const node = evt.target;
        unhighlightNode(node);
    });
    
    // Render molecular structures in nodes after layout is complete
    enhancedCy.ready(function() {
        renderMolecularStructuresInNodes();
    });
    
    console.log("Enhanced Cytoscape initialized");
}

// Get enhanced node styles with molecular structure support
function getEnhancedNodeStyles() {
    return [
        {
            selector: 'node',
            style: {
                'label': 'data(name)',
                'text-valign': 'bottom',
                'text-halign': 'center',
                'text-margin-y': 10,
                'color': '#333',
                'text-outline-width': 2,
                'text-outline-color': '#fff',
                'font-size': '12px',
                'font-weight': 'bold',
                'background-color': '#f8f9fa',
                'border-width': 3,
                'border-color': '#dee2e6',
                'width': function(ele) {
                    return getEnhancedNodeSize(ele.data('type')).width;
                },
                'height': function(ele) {
                    return getEnhancedNodeSize(ele.data('type')).height;
                },
                'shape': 'round-rectangle',
                'background-opacity': 0.9,
                'overlay-opacity': 0
            }
        },
        {
            selector: 'node[type="target"]',
            style: {
                'background-color': '#007bff',
                'border-color': '#0056b3',
                'color': '#fff',
                'text-outline-color': '#000',
                'shape': 'diamond',
                'width': 80,
                'height': 80
            }
        },
        {
            selector: 'node[type="scaffold"]',
            style: {
                'background-color': '#ffc107',
                'border-color': '#e0a800',
                'color': '#000',
                'shape': 'hexagon',
                'width': 100,
                'height': 100
            }
        },
        {
            selector: 'node[type="compound"]',
            style: {
                'background-color': '#28a745',
                'border-color': '#1e7e34',
                'color': '#fff',
                'text-outline-color': '#000',
                'width': 120,
                'height': 120,
                'border-width': function(ele) {
                    const violations = ele.data('lipinski_violations');
                    return violations > 0 ? 4 : 3;
                },
                'border-color': function(ele) {
                    const violations = ele.data('lipinski_violations');
                    if (violations === 0) return '#28a745';
                    if (violations === 1) return '#ffc107';
                    return '#dc3545';
                }
            }
        },
        {
            selector: 'node[type="fragment"]',
            style: {
                'background-color': '#17a2b8',
                'border-color': '#117a8b',
                'color': '#fff',
                'text-outline-color': '#000',
                'width': 80,
                'height': 80
            }
        },
        {
            selector: 'edge',
            style: {
                'width': 3,
                'line-color': '#6c757d',
                'curve-style': 'bezier',
                'target-arrow-shape': 'triangle',
                'target-arrow-color': '#6c757d',
                'arrow-scale': 1.2,
                'opacity': 0.8
            }
        },
        {
            selector: 'node:selected',
            style: {
                'border-color': '#ff6b6b',
                'border-width': 5,
                'overlay-opacity': 0.2,
                'overlay-color': '#ff6b6b'
            }
        },
        {
            selector: 'node.highlighted',
            style: {
                'border-color': '#ff6b6b',
                'border-width': 4,
                'overlay-opacity': 0.1,
                'overlay-color': '#ff6b6b'
            }
        }
    ];
}

// Get enhanced layout configuration
function getEnhancedLayout() {
    return {
        name: 'cose',
        idealEdgeLength: 150,
        nodeOverlap: 30,
        refresh: 20,
        fit: true,
        padding: 50,
        randomize: false,
        componentSpacing: 150,
        nodeRepulsion: 800000,
        edgeElasticity: 200,
        nestingFactor: 5,
        gravity: 100,
        numIter: 2000,
        initialTemp: 300,
        coolingFactor: 0.95,
        minTemp: 1.0,
        animate: true,
        animationDuration: 1000
    };
}

// Get enhanced node size based on type
function getEnhancedNodeSize(type) {
    switch (type) {
        case 'target':
            return { width: 80, height: 80 };
        case 'scaffold':
            return { width: 100, height: 100 };
        case 'compound':
            return { width: 120, height: 120 };
        case 'fragment':
            return { width: 80, height: 80 };
        default:
            return { width: 60, height: 60 };
    }
}

// Render molecular structures directly in nodes
function renderMolecularStructuresInNodes() {
    if (!enhancedCy || !smilesDrawer) return;

    console.log("Rendering molecular structures in nodes");

    enhancedCy.nodes().forEach(function(node) {
        const data = node.data();
        const smiles = data.smiles;

        if (smiles && (data.type === 'compound' || data.type === 'scaffold' || data.type === 'fragment')) {
            renderStructureInNode(node, smiles);
        }
    });
}

// Render structure in a specific node
function renderStructureInNode(node, smiles) {
    try {
        const nodeId = node.id();
        const position = node.renderedPosition();
        const size = getEnhancedNodeSize(node.data('type'));

        // Create a unique canvas for this node
        const canvasId = `structure-canvas-${nodeId}`;
        let canvas = document.getElementById(canvasId);

        if (!canvas) {
            canvas = document.createElement('canvas');
            canvas.id = canvasId;
            canvas.width = size.width - 20; // Leave some padding
            canvas.height = size.height - 20;
            canvas.style.position = 'absolute';
            canvas.style.pointerEvents = 'none';
            canvas.style.zIndex = '1000';
            document.body.appendChild(canvas);
        }

        // Position canvas over the node
        const cyContainer = document.getElementById('cy');
        const containerRect = cyContainer.getBoundingClientRect();
        canvas.style.left = (containerRect.left + position.x - canvas.width / 2) + 'px';
        canvas.style.top = (containerRect.top + position.y - canvas.height / 2) + 'px';

        // Parse and draw the molecule
        SmilesDrawer.parse(smiles, function(tree) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Create a temporary drawer with smaller size
            const nodeDrawer = new SmilesDrawer.Drawer({
                width: canvas.width,
                height: canvas.height,
                bondThickness: 1.5,
                bondLength: 12,
                atomVisualization: 'default',
                terminalCarbons: false,
                explicitHydrogens: false,
                compactDrawing: true,
                fontSizeLarge: 8,
                fontSizeSmall: 6,
                padding: 5
            });

            // Draw to canvas
            nodeDrawer.draw(tree, canvas, 'light', false);
        }, function(err) {
            console.warn(`Failed to parse SMILES ${smiles}:`, err);
        });

        // Update canvas position when node moves
        node.on('position', function() {
            const newPosition = node.renderedPosition();
            canvas.style.left = (containerRect.left + newPosition.x - canvas.width / 2) + 'px';
            canvas.style.top = (containerRect.top + newPosition.y - canvas.height / 2) + 'px';
        });

    } catch (error) {
        console.error(`Error rendering structure for node ${node.id()}:`, error);
    }
}

// Highlight node
function highlightNode(node) {
    node.addClass('highlighted');

    // Animate the node
    node.animate({
        style: {
            'width': function(ele) {
                const size = getEnhancedNodeSize(ele.data('type'));
                return size.width * 1.1;
            },
            'height': function(ele) {
                const size = getEnhancedNodeSize(ele.data('type'));
                return size.height * 1.1;
            }
        }
    }, {
        duration: 200,
        easing: 'ease-out'
    });
}

// Unhighlight node
function unhighlightNode(node) {
    node.removeClass('highlighted');

    // Animate back to original size
    node.animate({
        style: {
            'width': function(ele) {
                const size = getEnhancedNodeSize(ele.data('type'));
                return size.width;
            },
            'height': function(ele) {
                const size = getEnhancedNodeSize(ele.data('type'));
                return size.height;
            }
        }
    }, {
        duration: 200,
        easing: 'ease-out'
    });
}

// Show enhanced node details
function showEnhancedNodeDetails(node) {
    const data = node.data();
    console.log("Enhanced node clicked:", data);

    // Create detailed popup
    const popup = createEnhancedPopup(data);
    showPopup(popup, node.renderedPosition());
}

// Create enhanced popup with molecular structure
function createEnhancedPopup(nodeData) {
    const popupId = `popup-${nodeData.id}`;

    let content = `
        <div id="${popupId}" class="enhanced-popup">
            <div class="popup-header">
                <h5>${nodeData.name}</h5>
                <button class="popup-close" onclick="closePopup('${popupId}')">&times;</button>
            </div>
            <div class="popup-body">
    `;

    switch (nodeData.type) {
        case 'compound':
            content += `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>SMILES:</strong> <code>${nodeData.smiles}</code></p>
                        <p><strong>Activity:</strong> ${nodeData.activity} ${nodeData.activity_unit}</p>
                        <p><strong>pIC50:</strong> ${nodeData.pIC50?.toFixed(2) || 'N/A'}</p>
                        <p><strong>Lipinski Violations:</strong> ${nodeData.lipinski_violations}</p>
                    </div>
                    <div class="col-md-6">
                        <div class="structure-display" id="popup-structure-${nodeData.id}"></div>
                    </div>
                </div>
            `;
            break;

        case 'scaffold':
            content += `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>SMILES:</strong> <code>${nodeData.smiles}</code></p>
                        <p><strong>Compounds:</strong> ${nodeData.count}</p>
                    </div>
                    <div class="col-md-6">
                        <div class="structure-display" id="popup-structure-${nodeData.id}"></div>
                    </div>
                </div>
            `;
            break;

        case 'fragment':
            content += `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>SMILES:</strong> <code>${nodeData.smiles}</code></p>
                    </div>
                    <div class="col-md-6">
                        <div class="structure-display" id="popup-structure-${nodeData.id}"></div>
                    </div>
                </div>
            `;
            break;
    }

    content += `
            </div>
        </div>
    `;

    return content;
}

// Show popup at position
function showPopup(content, position) {
    // Remove existing popups
    $('.enhanced-popup').remove();

    // Add popup to body
    $('body').append(content);

    // Position popup
    const popup = $('.enhanced-popup').last();
    popup.css({
        position: 'absolute',
        left: position.x + 20,
        top: position.y - popup.height() / 2,
        zIndex: 10000
    });

    // Render structure in popup
    const nodeId = popup.attr('id').replace('popup-', '');
    const structureId = `popup-structure-${nodeId}`;

    // Find the node data
    const node = enhancedCy.getElementById(nodeId);
    if (node.length > 0) {
        const smiles = node.data('smiles');
        if (smiles) {
            renderLargeStructure(smiles, structureId);
        }
    }
}

// Render large structure for popup
function renderLargeStructure(smiles, containerId) {
    if (!smiles || !smilesDrawer) return;

    try {
        SmilesDrawer.parse(smiles, function(tree) {
            const largeDrawer = new SmilesDrawer.Drawer({
                width: 300,
                height: 200,
                bondThickness: 2,
                bondLength: 15,
                atomVisualization: 'default',
                terminalCarbons: false,
                explicitHydrogens: false,
                compactDrawing: false,
                fontSizeLarge: 12,
                fontSizeSmall: 10,
                padding: 10
            });

            largeDrawer.draw(tree, containerId, 'light', false);
        });
    } catch (error) {
        console.error("Error rendering large structure:", error);
        $(`#${containerId}`).html(`<p>Error rendering structure</p>`);
    }
}

// Close popup
function closePopup(popupId) {
    $(`#${popupId}`).remove();
}

// Show error message
function showError(message) {
    $('#loading-indicator').addClass('d-none');
    $('#error-message').removeClass('d-none').text(message);
}

// Export functions for global access
window.createEnhancedVisualization = createEnhancedVisualization;
window.closePopup = closePopup;
