import os
import sys
# DON'T CHANGE THIS !!!
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))
from flask import Flask, render_template, send_from_directory
from src.routes.upload import upload_bp
from src.routes.database import database_bp
from src.routes.standardization import standardization_bp
from src.routes.visualization import visualization_bp
from src.routes.filtering import filtering_bp
from src.routes.freewilson import freewilson_bp
from src.routes.visualization_mcs import visualization_mcs_bp
app = Flask(__name__, static_folder='static')
app.config['SECRET_KEY'] = os.urandom(24)
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(__file__), 'uploads')
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16 MB max upload size
# Ensure the upload folder exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
# Register blueprints
app.register_blueprint(upload_bp, url_prefix='/api/upload')
app.register_blueprint(database_bp, url_prefix='/api/database')
app.register_blueprint(standardization_bp, url_prefix='/api/standardize')
app.register_blueprint(visualization_bp, url_prefix='/api/visualization')
app.register_blueprint(filtering_bp, url_prefix='/api/filter')
app.register_blueprint(freewilson_bp, url_prefix='/api/freewilson')
app.register_blueprint(visualization_mcs_bp, url_prefix='/api/visualization_mcs')
@app.route('/')
def index():
    return send_from_directory(app.static_folder, 'index.html')
@app.route('/<path:path>')
def static_files(path):
    if path.endswith('.html'):
        return send_from_directory(app.static_folder, 'index.html')
    return send_from_directory(app.static_folder, path)
if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0')
