# ChemMap - Chemical Compound Hierarchical Mind Maps

## Overview
ChemMap is a web application that generates hierarchical mind maps of chemical compounds based on target activity. It helps medicinal chemists and researchers visualize structure-activity relationships and identify promising scaffolds.

## Features
- Upload CSV data with target, SMILES, and activity values
- Connect to local PostgreSQL ChEMBL database and query by UniProt ID
- Filter compounds by activity threshold (default 10μM)
- Break compounds into fragments and visualize hierarchical growth
- Color-code nodes based on activity (blue for low activity, red for high activity)
- Interactive visualization with downloadable SVG/PNG images
- Advanced Free Wilson analysis tool for R-group contribution analysis

## Installation

### Prerequisites
- Python 3.8+
- PostgreSQL (optional, for ChEMBL database connectivity)
- RDKit and other dependencies listed in requirements.txt

### Setup
1. Clone the repository or extract the provided files
2. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```
3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
4. Configure database connection (optional):
   - Set environment variables for ChEMBL database connection:
     ```
     export CHEMBL_DB_HOST=localhost
     export CHEMBL_DB_NAME=chembl
     export CHEMBL_DB_USER=postgres
     export CHEMBL_DB_PASSWORD=postgres
     export CHEMBL_DB_PORT=5432
     ```
   - Or modify the connection parameters in `src/routes/database.py`

## Running the Application
1. Start the Flask server:
   ```
   cd chemmap
   python src/main.py
   ```
2. Open a web browser and navigate to:
   ```
   http://localhost:5000
   ```

## Deployment
The application can be deployed using various methods:

### Option 1: Deploy with Flask's Built-in Server (Development Only)
```
python src/main.py
```

### Option 2: Deploy with Gunicorn (Recommended for Production)
1. Install Gunicorn:
   ```
   pip install gunicorn
   ```
2. Run with Gunicorn:
   ```
   gunicorn -w 4 -b 0.0.0.0:5000 'src.main:app'
   ```

### Option 3: Deploy with Docker
1. Build the Docker image:
   ```
   docker build -t chemmap .
   ```
2. Run the container:
   ```
   docker run -p 5000:5000 chemmap
   ```

## Usage Guide

### Uploading CSV Data
1. Navigate to the "Upload Data" tab
2. Select a CSV file with columns: target, smiles, activity
3. Click "Upload"
4. Set the activity threshold and click "Process Data"
5. View the visualization by clicking "View Visualization"

### Querying ChEMBL Database
1. Navigate to the "Database Query" tab
2. Enter a UniProt ID (e.g., P10275 for Androgen Receptor)
3. Set the activity threshold and click "Search"
4. Click "Process Data" to generate the visualization
5. View the visualization by clicking "View Visualization"

### Using the Visualization
- Click on nodes to view molecule information
- Double-click to expand/collapse nodes
- Use the controls to filter by activity threshold
- Change the color scheme between activity and Lipinski violations
- Download the visualization as SVG or PNG

### Free Wilson Analysis
1. Navigate to the "Free Wilson Analysis" tab
2. Select a processed dataset from the dropdown
3. Optionally enter a scaffold SMARTS pattern
4. Click "Run Analysis"
5. View the R-group contribution charts and statistics
6. Click "View Network" to see the visualization

## File Structure
- `src/main.py`: Main Flask application entry point
- `src/routes/`: API endpoints for different features
- `src/static/`: Frontend files (HTML, CSS, JavaScript)
- `src/uploads/`: Directory for uploaded and processed files

## Troubleshooting
- If database connection fails, ensure PostgreSQL is running and credentials are correct
- For visualization issues, check browser console for JavaScript errors
- If RDKit fails to load, ensure it's properly installed via requirements.txt

## License
This software is provided for educational and research purposes only.

## Acknowledgements
- RDKit for cheminformatics functionality
- Cytoscape.js for network visualization
- ChEMBL database for compound data
