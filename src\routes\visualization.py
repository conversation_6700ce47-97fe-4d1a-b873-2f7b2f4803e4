from flask import Blueprint, request, jsonify
import os
import json
import math
import logging
from rdkit import Chem
from rdkit.Chem import AllChem, BRICS, FragmentMatcher
from rdkit.Chem.Scaffolds import MurckoScaffold

visualization_bp = Blueprint('visualization', __name__)

# Directory to store visualization data
DATA_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads')
os.makedirs(DATA_FOLDER, exist_ok=True)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def generate_scaffold(mol):
    """Generate Murcko scaffold from molecule with improved robustness"""
    try:
        # Try standard Murcko scaffold first
        scaffold = MurckoScaffold.GetScaffoldForMol(mol)
        if scaffold and scaffold.GetNumAtoms() > 0:
            AllChem.Compute2DCoords(scaffold)
            return scaffold, Chem.MolToSmiles(scaffold)
        
        # If standard scaffold fails, try generic scaffold
        generic_scaffold = MurckoScaffold.MakeScaffoldGeneric(MurckoScaffold.GetScaffoldForMol(mol))
        if generic_scaffold and generic_scaffold.GetNumAtoms() > 0:
            AllChem.Compute2DCoords(generic_scaffold)
            return generic_scaffold, Chem.MolToSmiles(generic_scaffold)
        
        # If both fail, try to get the largest ring system
        if mol.GetNumAtoms() > 0:
            # Create a simple scaffold by removing all side chains
            params = Chem.RemoveHsParameters()
            params.removeIsotopes = True
            mol_no_h = Chem.RemoveHs(mol, params)
            
            # Get the largest fragment
            frags = Chem.GetMolFrags(mol_no_h, asMols=True)
            if frags:
                largest_frag = max(frags, key=lambda x: x.GetNumAtoms())
                if largest_frag and largest_frag.GetNumAtoms() > 0:
                    AllChem.Compute2DCoords(largest_frag)
                    return largest_frag, Chem.MolToSmiles(largest_frag)
        
        # If all else fails, return the original molecule as its own scaffold
        AllChem.Compute2DCoords(mol)
        return mol, Chem.MolToSmiles(mol)
    except Exception as e:
        logger.error(f"Scaffold generation error: {e}")
        return None, None

def fragment_molecule(mol):
    """Fragment molecule using multiple algorithms for better coverage"""
    fragments = []
    
    try:
        # Try BRICS fragmentation first
        brics_fragments = list(BRICS.BRICSDecompose(mol))
        
        for frag in brics_fragments:
            frag_mol = Chem.MolFromSmiles(frag)
            if frag_mol and frag_mol.GetNumAtoms() > 3:  # Only include significant fragments
                AllChem.Compute2DCoords(frag_mol)
                fragments.append((frag_mol, frag))
        
        # If BRICS doesn't yield enough fragments, try recursive decomposition
        if len(fragments) < 2:
            # Get ring systems
            ring_info = mol.GetRingInfo()
            if ring_info.NumRings() > 0:
                for ring_atoms in ring_info.AtomRings():
                    if len(ring_atoms) > 3:  # Only include significant rings
                        # Create a substructure from the ring
                        ring_mol = Chem.PathToSubmol(mol, ring_atoms)
                        if ring_mol and ring_mol.GetNumAtoms() > 0:
                            try:
                                Chem.SanitizeMol(ring_mol)
                                AllChem.Compute2DCoords(ring_mol)
                                ring_smiles = Chem.MolToSmiles(ring_mol)
                                fragments.append((ring_mol, ring_smiles))
                            except:
                                pass
        
        # If still not enough fragments, add functional groups
        if len(fragments) < 2:
            from rdkit.Chem import ChemicalFeatures
            factory = ChemicalFeatures.BuildFeatureFactory()
            feats = factory.GetFeaturesForMol(mol)
            
            for feat in feats:
                atoms = feat.GetAtomIds()
                if len(atoms) > 2:  # Only include significant features
                    try:
                        feat_mol = Chem.PathToSubmol(mol, atoms)
                        if feat_mol and feat_mol.GetNumAtoms() > 0:
                            Chem.SanitizeMol(feat_mol)
                            AllChem.Compute2DCoords(feat_mol)
                            feat_smiles = Chem.MolToSmiles(feat_mol)
                            fragments.append((feat_mol, feat_smiles))
                    except:
                        pass
        
        return fragments
    except Exception as e:
        logger.error(f"Fragmentation error: {e}")
        return []

def create_hierarchical_structure(compounds):
    """Create hierarchical structure for visualization with improved robustness"""
    # Group compounds by scaffold
    scaffolds = {}
    nodes = []
    edges = []
    
    # Add target node
    if compounds and len(compounds) > 0:
        target_name = compounds[0].get('target', 'Unknown')
        target_id = f"target_{target_name.replace(' ', '_').lower()}"
        nodes.append({
            'id': target_id,
            'type': 'target',
            'name': target_name,
            'visible': True,
            'expanded': True,
            'has_children': True
        })
    else:
        # If no compounds, create a default target node
        target_id = "target_unknown"
        nodes.append({
            'id': target_id,
            'type': 'target',
            'name': 'Unknown',
            'visible': True,
            'expanded': True,
            'has_children': False
        })
        
        logger.warning("No compounds found for visualization")
        return {
            'nodes': nodes,
            'edges': edges,
            'scaffolds': []
        }
    
    # Process each compound
    for idx, compound in enumerate(compounds):
        smiles = compound.get('smiles')
        mol = Chem.MolFromSmiles(smiles)
        
        if not mol:
            logger.warning(f"Could not create molecule from SMILES: {smiles}")
            continue
        
        # Generate scaffold
        scaffold_mol, scaffold_smiles = generate_scaffold(mol)
        
        if not scaffold_mol or not scaffold_smiles:
            logger.warning(f"Could not generate scaffold for compound {idx}: {smiles}")
            # Use the molecule itself as scaffold
            scaffold_mol = mol
            scaffold_smiles = smiles
        
        # Add scaffold if not already present
        if scaffold_smiles not in scaffolds:
            scaffold_id = f"scaffold_{len(scaffolds)}"
            scaffolds[scaffold_smiles] = {
                'id': scaffold_id,
                'smiles': scaffold_smiles,
                'compounds': []
            }
            
            # Add scaffold node
            nodes.append({
                'id': scaffold_id,
                'type': 'scaffold',
                'name': f"Core {len(scaffolds)}",
                'smiles': scaffold_smiles,
                'svg': Chem.MolToMolBlock(scaffold_mol),
                'visible': True,
                'expanded': False,
                'has_children': True,
                'count': 0
            })
            
            # Connect target to scaffold
            edges.append({
                'source': target_id,
                'target': scaffold_id,
                'visible': True
            })
        
        # Add compound to scaffold
        scaffold_id = scaffolds[scaffold_smiles]['id']
        scaffolds[scaffold_smiles]['compounds'].append(compound)
        scaffolds[scaffold_smiles]['count'] = len(scaffolds[scaffold_smiles]['compounds'])
        
        # Update scaffold node count
        for node in nodes:
            if node['id'] == scaffold_id:
                node['count'] = len(scaffolds[scaffold_smiles]['compounds'])
        
        # Add compound node
        compound_id = f"compound_{idx}"
        pic50 = compound.get('pIC50', 0)
        
        # Ensure we have a valid activity value
        activity = compound.get('activity', 0)
        activity_type = compound.get('activity_type', 'pIC50')
        activity_unit = 'pIC50' if activity_type == 'pIC50' else 'μM'
        
        nodes.append({
            'id': compound_id,
            'type': 'compound',
            'name': compound.get('compound_name', f"Compound {idx+1}"),
            'smiles': smiles,
            'svg': compound.get('svg', Chem.MolToMolBlock(mol)),
            'activity': activity,
            'activity_type': activity_type,
            'activity_unit': activity_unit,
            'pIC50': pic50,
            'visible': False,
            'expanded': False,
            'has_children': True,
            'parent': scaffold_id,
            'lipinski_violations': compound.get('lipinski_violations', 0)
        })
        
        # Connect scaffold to compound
        edges.append({
            'source': scaffold_id,
            'target': compound_id,
            'visible': False
        })
        
        # Fragment the compound
        fragments = fragment_molecule(mol)
        
        # Add fragment nodes
        for i, (frag_mol, frag_smiles) in enumerate(fragments):
            frag_id = f"fragment_{idx}_{i}"
            
            nodes.append({
                'id': frag_id,
                'type': 'fragment',
                'name': f"Fragment {i+1}",
                'smiles': frag_smiles,
                'svg': Chem.MolToMolBlock(frag_mol),
                'visible': False,
                'expanded': False,
                'has_children': False,
                'parent': compound_id
            })
            
            # Connect compound to fragment
            edges.append({
                'source': compound_id,
                'target': frag_id,
                'visible': False
            })
    
    # Update scaffold nodes with compound counts
    for node in nodes:
        if node['type'] == 'scaffold':
            scaffold_smiles = node['smiles']
            if scaffold_smiles in scaffolds:
                node['count'] = len(scaffolds[scaffold_smiles]['compounds'])
    
    # Log visualization statistics
    logger.info(f"Created visualization with {len(nodes)} nodes, {len(edges)} edges, and {len(scaffolds)} scaffolds")
    
    return {
        'nodes': nodes,
        'edges': edges,
        'scaffolds': [{'id': s['id'], 'smiles': smiles, 'count': s['count']} 
                     for smiles, s in scaffolds.items()]
    }

@visualization_bp.route('/hierarchical', methods=['POST'])
def create_hierarchical_visualization():
    """Create hierarchical visualization from standardized data"""
    data_id = request.json.get('data_id')
    
    if not data_id:
        return jsonify({'success': False, 'error': 'Data ID is required'}), 400
    
    # Check if data exists
    data_path = os.path.join(DATA_FOLDER, f"{data_id}.json")
    if not os.path.exists(data_path):
        return jsonify({'success': False, 'error': 'Data not found'}), 404
    
    try:
        # Load the data
        with open(data_path, 'r') as f:
            data = json.load(f)
        
        compounds = data.get('compounds', [])
        
        if not compounds:
            return jsonify({'success': False, 'error': 'No compounds found in data'}), 400
        
        # Create hierarchical structure
        hierarchy = create_hierarchical_structure(compounds)
        
        # Save visualization data
        viz_id = f"{data_id}_viz"
        viz_path = os.path.join(DATA_FOLDER, f"{viz_id}.json")
        
        with open(viz_path, 'w') as f:
            json.dump({
                'visualization': hierarchy,
                'original_data_id': data_id,
                'compound_count': len(compounds),
                'scaffold_count': len(hierarchy['scaffolds'])
            }, f)
        
        return jsonify({
            'success': True,
            'message': f'Created hierarchical visualization with {len(hierarchy["nodes"])} nodes and {len(hierarchy["edges"])} edges',
            'data_id': viz_id,
            'node_count': len(hierarchy['nodes']),
            'edge_count': len(hierarchy['edges']),
            'scaffold_count': len(hierarchy['scaffolds'])
        })
        
    except Exception as e:
        logger.error(f"Visualization error: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@visualization_bp.route('/visualization/<viz_id>', methods=['GET'])
def get_visualization(viz_id):
    """Get visualization data by ID"""
    viz_path = os.path.join(DATA_FOLDER, f"{viz_id}.json")
    
    if not os.path.exists(viz_path):
        return jsonify({'success': False, 'error': 'Visualization not found'}), 404
    
    try:
        with open(viz_path, 'r') as f:
            data = json.load(f)
        
        return jsonify({
            'success': True,
            'data': data
        })
    except Exception as e:
        logger.error(f"Error retrieving visualization: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500
