from flask import Blueprint, request, jsonify
import os
import json
import pandas as pd
import psycopg2
from psycopg2 import sql
from rdkit import Chem
from rdkit.Chem import AllChem

database_bp = Blueprint('database', __name__)

# Directory to store query results
QUERY_RESULTS_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads')
os.makedirs(QUERY_RESULTS_FOLDER, exist_ok=True)

def get_db_connection():
    """Create a connection to the PostgreSQL ChEMBL database"""
    try:
        conn = psycopg2.connect(
            host=os.getenv('CHEMBL_DB_HOST', 'localhost'),
            database=os.getenv('CHEMBL_DB_NAME', 'chembl'),
            user=os.getenv('CHEMBL_DB_USER', 'postgres'),
            password=os.getenv('CHEMBL_DB_PASSWORD', 'postgres'),
            port=os.getenv('CHEMBL_DB_PORT', '5432')
        )
        return conn
    except Exception as e:
        print(f"Database connection error: {e}")
        return None

def standardize_activity_to_pic50(activity_value, activity_type, activity_unit):
    """Convert activity values to pIC50 standard"""
    try:
        value = float(activity_value)
        
        # Convert to M (molar) if needed
        if activity_unit == 'nM':
            value = value * 1e-9
        elif activity_unit == 'μM' or activity_unit == 'uM':
            value = value * 1e-6
        elif activity_unit == 'mM':
            value = value * 1e-3
        
        # Convert to pIC50 = -log10(IC50)
        if activity_type in ['IC50', 'EC50', 'Ki', 'Kd']:
            return -1 * (math.log10(value) if value > 0 else 0)
        elif activity_type == 'pIC50':
            return value
        else:
            return None  # Unsupported activity type
    except:
        return None

@database_bp.route('/connect', methods=['GET'])
def test_connection():
    """Test the connection to the ChEMBL database"""
    conn = get_db_connection()
    if conn:
        conn.close()
        return jsonify({'success': True, 'message': 'Successfully connected to ChEMBL database'})
    else:
        return jsonify({'success': False, 'error': 'Failed to connect to ChEMBL database'}), 500

@database_bp.route('/search/uniprot', methods=['GET'])
def search_by_uniprot():
    """Search compounds by UniProt ID"""
    uniprot_id = request.args.get('uniprot_id')
    activity_threshold = float(request.args.get('threshold', 10))  # Default 10μM
    
    if not uniprot_id:
        return jsonify({'success': False, 'error': 'UniProt ID is required'}), 400
    
    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'error': 'Database connection failed'}), 500
    
    try:
        cursor = conn.cursor()
        
        # Query to get compounds targeting a protein with the given UniProt ID
        query = """
        SELECT 
            cs.canonical_smiles,
            act.standard_value,
            act.standard_type,
            act.standard_units,
            act.pchembl_value,
            td.pref_name as target_name,
            md.chembl_id,
            md.pref_name as compound_name
        FROM 
            target_dictionary td
        JOIN 
            target_components tc ON td.tid = tc.tid
        JOIN 
            component_sequences cs_seq ON tc.component_id = cs_seq.component_id
        JOIN 
            activities act ON td.tid = act.tid
        JOIN 
            compound_structures cs ON act.molregno = cs.molregno
        JOIN 
            molecule_dictionary md ON act.molregno = md.molregno
        WHERE 
            cs_seq.accession = %s
            AND act.standard_type IN ('IC50', 'EC50', 'Ki', 'Kd', 'pIC50')
            AND act.standard_relation IN ('=', '<', '<=')
            AND act.standard_value IS NOT NULL
            AND cs.canonical_smiles IS NOT NULL
        ORDER BY 
            act.standard_value ASC
        """
        
        cursor.execute(query, (uniprot_id,))
        rows = cursor.fetchall()
        
        if not rows:
            return jsonify({'success': False, 'error': f'No compounds found for UniProt ID: {uniprot_id}'}), 404
        
        # Process the results
        compounds = []
        for row in rows:
            smiles, std_value, std_type, std_units, pchembl, target_name, chembl_id, compound_name = row
            
            # Convert to μM for threshold comparison if needed
            value_in_um = std_value
            if std_units == 'nM':
                value_in_um = std_value / 1000
            elif std_units == 'mM':
                value_in_um = std_value * 1000
            
            # Skip compounds above threshold
            if value_in_um > activity_threshold:
                continue
            
            # Generate molecule from SMILES
            mol = Chem.MolFromSmiles(smiles)
            if mol:
                # Generate 2D coordinates for visualization
                AllChem.Compute2DCoords(mol)
                
                # Calculate pIC50 if not provided
                pic50 = pchembl if pchembl else standardize_activity_to_pic50(std_value, std_type, std_units)
                
                compound = {
                    'target': target_name,
                    'uniprot_id': uniprot_id,
                    'chembl_id': chembl_id,
                    'compound_name': compound_name,
                    'smiles': smiles,
                    'activity_type': std_type,
                    'activity_value': float(std_value),
                    'activity_unit': std_units,
                    'pIC50': pic50,
                    'svg': Chem.MolToMolBlock(mol)
                }
                compounds.append(compound)
        
        cursor.close()
        conn.close()
        
        if not compounds:
            return jsonify({
                'success': False, 
                'error': f'No compounds found below threshold of {activity_threshold}μM for UniProt ID: {uniprot_id}'
            }), 404
        
        # Save results to a JSON file
        import uuid
        data_id = f"uniprot_{uniprot_id}_{uuid.uuid4()}"
        data_path = os.path.join(QUERY_RESULTS_FOLDER, f"{data_id}.json")
        
        with open(data_path, 'w') as f:
            json.dump({
                'compounds': compounds,
                'uniprot_id': uniprot_id,
                'threshold': activity_threshold
            }, f)
        
        return jsonify({
            'success': True,
            'message': f'Found {len(compounds)} compounds for {uniprot_id} below {activity_threshold}μM',
            'data_id': data_id,
            'compound_count': len(compounds)
        })
        
    except Exception as e:
        if conn:
            conn.close()
        return jsonify({'success': False, 'error': str(e)}), 500

@database_bp.route('/targets', methods=['GET'])
def get_targets():
    """Get a list of available targets in the database"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'error': 'Database connection failed'}), 500
    
    try:
        cursor = conn.cursor()
        
        query = """
        SELECT DISTINCT 
            td.pref_name, 
            cs.accession as uniprot_id,
            td.organism,
            COUNT(DISTINCT act.molregno) as compound_count
        FROM 
            target_dictionary td
        JOIN 
            target_components tc ON td.tid = tc.tid
        JOIN 
            component_sequences cs ON tc.component_id = cs.component_id
        JOIN 
            activities act ON td.tid = act.tid
        WHERE 
            cs.accession IS NOT NULL
            AND act.standard_type IN ('IC50', 'EC50', 'Ki', 'Kd', 'pIC50')
        GROUP BY 
            td.pref_name, cs.accession, td.organism
        HAVING 
            COUNT(DISTINCT act.molregno) > 5
        ORDER BY 
            compound_count DESC
        LIMIT 100
        """
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        targets = []
        for row in rows:
            target_name, uniprot_id, organism, compound_count = row
            targets.append({
                'name': target_name,
                'uniprot_id': uniprot_id,
                'organism': organism,
                'compound_count': compound_count
            })
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'success': True,
            'targets': targets
        })
        
    except Exception as e:
        if conn:
            conn.close()
        return jsonify({'success': False, 'error': str(e)}), 500
