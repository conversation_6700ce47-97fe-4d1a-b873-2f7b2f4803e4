// Advanced Molecular Visualization with Core/R-group Detection
let enhancedCy; // Enhanced Cytoscape instance
let smilesDrawer; // SmilesDrawer instance
let moleculeImages = new Map(); // Cache for molecule images
let coreNetworkData = null; // Store core network data

$(document).ready(function() {
    console.log("Advanced molecular visualization loaded");

    // Initialize SmilesDrawer with optimized settings
    if (typeof SmilesDrawer !== 'undefined') {
        smilesDrawer = new SmilesDrawer.Drawer({
            width: 200,
            height: 200,
            bondThickness: 2.5,
            bondLength: 18,
            shortBondLength: 0.85,
            bondSpacing: 0.18 * 18,
            atomVisualization: 'default',
            isomeric: true,
            debug: false,
            terminalCarbons: false,
            explicitHydrogens: false,
            overlapSensitivity: 0.42,
            overlapResolutionIterations: 1,
            compactDrawing: true,
            fontSizeLarge: 14,
            fontSizeSmall: 11,
            padding: 15.0,
            experimentalSSSR: true,
            kkThreshold: 0.1,
            kkInnerThreshold: 0.1,
            kkMaxIteration: 20000,
            kkMaxInnerIteration: 50,
            kkMaxEnergy: 1e9
        });
    }

    // Initialize RDKit if available
    if (typeof RDKit !== 'undefined' && window.RDKit) {
        console.log("RDKit is available for enhanced molecular rendering");
    }
});

// Create enhanced visualization with automatic core/R-group detection
function createEnhancedVisualization(dataId, threshold = 10) {
    console.log("Creating enhanced molecular visualization for data ID:", dataId);

    // Show loading indicator
    $('#loading-indicator').removeClass('d-none');
    $('#error-message').addClass('d-none');

    // First create core network with automatic detection
    createCoreNetworkVisualization(dataId, threshold);
}

// Create core network visualization with automatic core/R-group detection
function createCoreNetworkVisualization(dataId, threshold = 10) {
    console.log("Creating core network visualization with automatic detection");

    $.ajax({
        url: '/api/visualization_mcs/core-network',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            data_id: dataId,
            threshold: threshold
        }),
        success: function(response) {
            console.log("Core network visualization created:", response);
            if (response.success) {
                // Load the core network data
                loadCoreNetworkVisualization(response.data_id);
            } else {
                // Fallback to hierarchical visualization
                console.log("Core network failed, falling back to hierarchical");
                createHierarchicalVisualization(dataId, threshold);
            }
        },
        error: function(xhr, status, error) {
            console.error("Core network creation error:", error);
            // Fallback to hierarchical visualization
            createHierarchicalVisualization(dataId, threshold);
        }
    });
}

// Fallback hierarchical visualization
function createHierarchicalVisualization(dataId, threshold = 10) {
    $.ajax({
        url: '/api/visualization/hierarchical',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            data_id: dataId,
            threshold: threshold
        }),
        success: function(response) {
            console.log("Hierarchical visualization created:", response);
            if (response.success) {
                loadEnhancedVisualization(response.data_id);
            } else {
                showError(`Failed to create visualization: ${response.error}`);
            }
        },
        error: function(xhr, status, error) {
            console.error("Visualization creation error:", error);
            showError(`Error creating visualization: ${error}`);
        }
    });
}

// Load core network visualization data
function loadCoreNetworkVisualization(vizId) {
    $.ajax({
        url: `/api/visualization/visualization/${vizId}`,
        type: 'GET',
        success: function(response) {
            console.log("Core network visualization data loaded:", response);
            if (response.success) {
                const data = response.data;
                coreNetworkData = data.visualization;

                // Initialize enhanced Cytoscape with core network
                initMolecularCytoscape(coreNetworkData, 'core-network');

                // Hide loading indicator
                $('#loading-indicator').addClass('d-none');
            } else {
                showError(`Failed to load core network: ${response.error}`);
            }
        },
        error: function(xhr, status, error) {
            console.error("Core network loading error:", error);
            showError(`Error loading core network: ${error}`);
        }
    });
}

// Load enhanced visualization data (fallback)
function loadEnhancedVisualization(vizId) {
    $.ajax({
        url: `/api/visualization/visualization/${vizId}`,
        type: 'GET',
        success: function(response) {
            console.log("Enhanced visualization data loaded:", response);
            if (response.success) {
                const data = response.data;
                const visualization = data.visualization;

                // Initialize enhanced Cytoscape
                initMolecularCytoscape(visualization, 'hierarchical');

                // Hide loading indicator
                $('#loading-indicator').addClass('d-none');
            } else {
                showError(`Failed to load visualization: ${response.error}`);
            }
        },
        error: function(xhr, status, error) {
            console.error("Visualization loading error:", error);
            showError(`Error loading visualization: ${error}`);
        }
    });
}

// Initialize molecular Cytoscape with proper image rendering
function initMolecularCytoscape(visualization, type = 'core-network') {
    const nodes = visualization.nodes || [];
    const edges = visualization.edges || [];

    console.log(`Initializing molecular Cytoscape (${type}) with ${nodes.length} nodes and ${edges.length} edges`);

    // Clear existing visualization
    if (enhancedCy) {
        enhancedCy.destroy();
    }

    // Pre-generate molecular images for all nodes
    generateMolecularImages(nodes).then(() => {
        // Prepare elements with molecular image data
        const elements = {
            nodes: nodes.filter(node => node.visible !== false).map(node => ({
                data: {
                    id: node.id,
                    type: node.type,
                    name: node.name || node.id,
                    smiles: node.smiles || '',
                    svg: node.svg || '',
                    activity: node.activity || 0,
                    activity_unit: node.activity_unit || 'μM',
                    pIC50: node.pIC50 || 0,
                    parent: node.parent || null,
                    expanded: node.expanded || false,
                    has_children: node.has_children || false,
                    lipinski_violations: node.lipinski_violations || 0,
                    count: node.count || 0,
                    rgroups: node.rgroups || [],
                    core_id: node.core_id || null,
                    // Add molecular image data
                    moleculeImage: moleculeImages.get(node.id) || null
                },
                classes: node.type
            })),
            edges: edges.filter(edge => edge.visible !== false).map(edge => ({
                data: {
                    source: edge.source,
                    target: edge.target
                }
            }))
        };

        // Initialize enhanced Cytoscape with molecular images
        enhancedCy = cytoscape({
            container: document.getElementById('cy'),
            elements: elements,
            style: getMolecularNodeStyles(),
            layout: getMolecularLayout(type)
        });

        // Add event listeners
        enhancedCy.on('tap', 'node', function(evt) {
            const node = evt.target;
            showMolecularNodeDetails(node);
        });

        enhancedCy.on('mouseover', 'node', function(evt) {
            const node = evt.target;
            highlightMolecularNode(node);
        });

        enhancedCy.on('mouseout', 'node', function(evt) {
            const node = evt.target;
            unhighlightMolecularNode(node);
        });

        // Apply molecular images to nodes after layout
        enhancedCy.ready(function() {
            applyMolecularImagesToNodes();
            console.log("Molecular Cytoscape initialized with images");
        });
    });
}

// Generate molecular images for all nodes
async function generateMolecularImages(nodes) {
    console.log("Generating molecular images for nodes");

    const promises = nodes.map(async (node) => {
        if (node.smiles && (node.type === 'compound' || node.type === 'core' || node.type === 'scaffold' || node.type === 'fragment' || node.type === 'rgroup')) {
            try {
                const imageData = await generateMoleculeImage(node.smiles, node.type);
                if (imageData) {
                    moleculeImages.set(node.id, imageData);
                }
            } catch (error) {
                console.warn(`Failed to generate image for node ${node.id}:`, error);
            }
        }
    });

    await Promise.all(promises);
    console.log(`Generated ${moleculeImages.size} molecular images`);
}

// Generate a single molecule image
function generateMoleculeImage(smiles, nodeType) {
    return new Promise((resolve, reject) => {
        try {
            // Determine image size based on node type
            const sizes = {
                'core': { width: 120, height: 120 },
                'compound': { width: 100, height: 100 },
                'scaffold': { width: 110, height: 110 },
                'fragment': { width: 80, height: 80 },
                'rgroup': { width: 70, height: 70 }
            };

            const size = sizes[nodeType] || { width: 90, height: 90 };

            // Try RDKit first if available
            if (typeof RDKit !== 'undefined' && window.RDKit) {
                try {
                    const mol = RDKit.Molecule.fromSmiles(smiles);
                    if (mol) {
                        const svg = mol.toSVG(size.width, size.height);
                        mol.delete(); // Clean up RDKit object

                        // Convert SVG to data URL
                        const svgBlob = new Blob([svg], { type: 'image/svg+xml' });
                        const url = URL.createObjectURL(svgBlob);
                        resolve({
                            type: 'svg',
                            data: svg,
                            url: url,
                            width: size.width,
                            height: size.height
                        });
                        return;
                    }
                } catch (rdkitError) {
                    console.warn("RDKit rendering failed, falling back to SmilesDrawer:", rdkitError);
                }
            }

            // Fallback to SmilesDrawer
            if (typeof SmilesDrawer !== 'undefined') {
                SmilesDrawer.parse(smiles, function(tree) {
                    // Create a temporary canvas
                    const canvas = document.createElement('canvas');
                    canvas.width = size.width;
                    canvas.height = size.height;

                    const drawer = new SmilesDrawer.Drawer({
                        width: size.width,
                        height: size.height,
                        bondThickness: 2,
                        bondLength: 15,
                        atomVisualization: 'default',
                        terminalCarbons: false,
                        explicitHydrogens: false,
                        compactDrawing: true,
                        fontSizeLarge: 12,
                        fontSizeSmall: 10,
                        padding: 8
                    });

                    drawer.draw(tree, canvas, 'light', false);

                    // Convert canvas to data URL
                    const dataUrl = canvas.toDataURL('image/png');
                    resolve({
                        type: 'canvas',
                        data: dataUrl,
                        url: dataUrl,
                        width: size.width,
                        height: size.height
                    });
                }, function(error) {
                    console.warn("SmilesDrawer parsing failed:", error);
                    reject(error);
                });
            } else {
                reject(new Error("No molecular rendering library available"));
            }
        } catch (error) {
            reject(error);
        }
    });
}

// Get molecular node styles with image backgrounds
function getMolecularNodeStyles() {
    return [
        {
            selector: 'node',
            style: {
                'label': function(ele) {
                    const data = ele.data();
                    let label = data.name || data.id;

                    // Add activity info for compounds
                    if (data.type === 'compound' && data.activity) {
                        label += `\n${data.activity} ${data.activity_unit}`;
                        if (data.pIC50) {
                            label += `\npIC50: ${data.pIC50.toFixed(2)}`;
                        }
                    }

                    // Add count for cores
                    if (data.type === 'core' && data.count) {
                        label += `\n(${data.count} compounds)`;
                    }

                    return label;
                },
                'text-valign': 'bottom',
                'text-halign': 'center',
                'text-margin-y': 15,
                'color': '#333',
                'text-outline-width': 2,
                'text-outline-color': '#fff',
                'font-size': '11px',
                'font-weight': 'bold',
                'text-wrap': 'wrap',
                'text-max-width': '120px',
                'background-color': '#ffffff',
                'background-opacity': 0.95,
                'border-width': 3,
                'border-color': function(ele) {
                    const data = ele.data();
                    if (data.type === 'compound') {
                        const violations = data.lipinski_violations || 0;
                        if (violations === 0) return '#28a745'; // Green
                        if (violations === 1) return '#ffc107'; // Yellow
                        return '#dc3545'; // Red
                    }
                    return '#6c757d';
                },
                'width': function(ele) {
                    return getMolecularNodeSize(ele.data('type')).width;
                },
                'height': function(ele) {
                    return getMolecularNodeSize(ele.data('type')).height;
                },
                'shape': 'round-rectangle',
                'overlay-opacity': 0
            }
        },
        {
            selector: 'node[type="target"]',
            style: {
                'background-color': '#007bff',
                'border-color': '#0056b3',
                'color': '#fff',
                'text-outline-color': '#000',
                'shape': 'diamond',
                'width': 80,
                'height': 80
            }
        },
        {
            selector: 'node[type="core"]',
            style: {
                'background-color': '#fff3cd',
                'border-color': '#ffc107',
                'border-width': 4,
                'color': '#000',
                'shape': 'hexagon',
                'width': 140,
                'height': 140,
                'text-margin-y': 20
            }
        },
        {
            selector: 'node[type="scaffold"]',
            style: {
                'background-color': '#fff3cd',
                'border-color': '#ffc107',
                'color': '#000',
                'shape': 'hexagon',
                'width': 120,
                'height': 120
            }
        },
        {
            selector: 'node[type="compound"]',
            style: {
                'background-color': function(ele) {
                    const pIC50 = ele.data('pIC50');
                    if (pIC50) {
                        // Color based on activity: red (low) to green (high)
                        const min = 5, max = 9;
                        const value = Math.max(min, Math.min(max, pIC50));
                        const ratio = (value - min) / (max - min);
                        const r = Math.round(255 * (1 - ratio));
                        const g = Math.round(255 * ratio);
                        return `rgb(${r}, ${g}, 100)`;
                    }
                    return '#e9ecef';
                },
                'color': '#000',
                'text-outline-color': '#fff',
                'width': 110,
                'height': 110,
                'border-width': 3
            }
        },
        {
            selector: 'node[type="rgroup"]',
            style: {
                'background-color': '#d1ecf1',
                'border-color': '#17a2b8',
                'color': '#000',
                'text-outline-color': '#fff',
                'width': 80,
                'height': 80
            }
        },
        {
            selector: 'node[type="fragment"]',
            style: {
                'background-color': '#d1ecf1',
                'border-color': '#17a2b8',
                'color': '#000',
                'text-outline-color': '#fff',
                'width': 80,
                'height': 80
            }
        },
        {
            selector: 'edge',
            style: {
                'width': 3,
                'line-color': '#6c757d',
                'curve-style': 'bezier',
                'target-arrow-shape': 'triangle',
                'target-arrow-color': '#6c757d',
                'arrow-scale': 1.2,
                'opacity': 0.8
            }
        },
        {
            selector: 'node:selected',
            style: {
                'border-color': '#ff6b6b',
                'border-width': 5,
                'overlay-opacity': 0.2,
                'overlay-color': '#ff6b6b'
            }
        },
        {
            selector: 'node.highlighted',
            style: {
                'border-color': '#ff6b6b',
                'border-width': 4,
                'overlay-opacity': 0.1,
                'overlay-color': '#ff6b6b'
            }
        }
    ];
}

// Apply molecular images to nodes
function applyMolecularImagesToNodes() {
    if (!enhancedCy) return;

    console.log("Applying molecular images to nodes");

    enhancedCy.nodes().forEach(function(node) {
        const data = node.data();
        const imageData = data.moleculeImage;

        if (imageData && imageData.url) {
            // Apply the molecular image as background
            node.style({
                'background-image': imageData.url,
                'background-fit': 'contain',
                'background-position-x': '50%',
                'background-position-y': '40%', // Slightly up to leave room for label
                'background-width': '80%',
                'background-height': '60%',
                'background-opacity': 1
            });
        }
    });
}

// Get molecular layout configuration
function getMolecularLayout(type = 'core-network') {
    if (type === 'core-network') {
        return {
            name: 'concentric',
            concentric: function(node) {
                // Place cores in center, compounds around them, R-groups on outside
                const nodeType = node.data('type');
                if (nodeType === 'core') return 10;
                if (nodeType === 'compound') return 5;
                if (nodeType === 'rgroup' || nodeType === 'fragment') return 1;
                return 3;
            },
            levelWidth: function(nodes) {
                return Math.max(2, Math.ceil(nodes.length / 4));
            },
            minNodeSpacing: 80,
            padding: 50,
            fit: true,
            animate: true,
            animationDuration: 1500
        };
    } else {
        return {
            name: 'cose',
            idealEdgeLength: 120,
            nodeOverlap: 40,
            refresh: 20,
            fit: true,
            padding: 60,
            randomize: false,
            componentSpacing: 120,
            nodeRepulsion: 600000,
            edgeElasticity: 150,
            nestingFactor: 5,
            gravity: 80,
            numIter: 1500,
            initialTemp: 250,
            coolingFactor: 0.95,
            minTemp: 1.0,
            animate: true,
            animationDuration: 1200
        };
    }
}

// Get molecular node size based on type
function getMolecularNodeSize(type) {
    switch (type) {
        case 'target':
            return { width: 80, height: 80 };
        case 'core':
            return { width: 140, height: 140 };
        case 'scaffold':
            return { width: 120, height: 120 };
        case 'compound':
            return { width: 110, height: 110 };
        case 'rgroup':
            return { width: 80, height: 80 };
        case 'fragment':
            return { width: 80, height: 80 };
        default:
            return { width: 90, height: 90 };
    }
}

// Highlight molecular node
function highlightMolecularNode(node) {
    node.addClass('highlighted');

    // Animate the node with glow effect
    node.animate({
        style: {
            'border-width': 5,
            'border-color': '#ff6b6b',
            'overlay-opacity': 0.2,
            'overlay-color': '#ff6b6b'
        }
    }, {
        duration: 200,
        easing: 'ease-out'
    });

    // Show quick tooltip with activity info
    showQuickTooltip(node);
}

// Unhighlight molecular node
function unhighlightMolecularNode(node) {
    node.removeClass('highlighted');

    // Animate back to original style
    node.animate({
        style: {
            'border-width': function(ele) {
                return ele.data('type') === 'core' ? 4 : 3;
            },
            'border-color': function(ele) {
                const data = ele.data();
                if (data.type === 'compound') {
                    const violations = data.lipinski_violations || 0;
                    if (violations === 0) return '#28a745';
                    if (violations === 1) return '#ffc107';
                    return '#dc3545';
                } else if (data.type === 'core') {
                    return '#ffc107';
                } else if (data.type === 'rgroup' || data.type === 'fragment') {
                    return '#17a2b8';
                }
                return '#6c757d';
            },
            'overlay-opacity': 0
        }
    }, {
        duration: 200,
        easing: 'ease-out'
    });

    // Hide tooltip
    hideQuickTooltip();
}

// Show quick tooltip on hover
function showQuickTooltip(node) {
    const data = node.data();
    const position = node.renderedPosition();

    let tooltipContent = `<strong>${data.name}</strong><br>`;

    if (data.type === 'compound') {
        tooltipContent += `Activity: ${data.activity} ${data.activity_unit}<br>`;
        if (data.pIC50) {
            tooltipContent += `pIC50: ${data.pIC50.toFixed(2)}<br>`;
        }
        tooltipContent += `Lipinski violations: ${data.lipinski_violations || 0}`;
    } else if (data.type === 'core') {
        tooltipContent += `Core structure<br>`;
        tooltipContent += `${data.count || 0} compounds`;
    } else if (data.type === 'rgroup') {
        tooltipContent += `R-group`;
    }

    // Remove existing tooltip
    $('.quick-tooltip').remove();

    // Create and show tooltip
    const tooltip = $(`
        <div class="quick-tooltip" style="
            position: absolute;
            left: ${position.x + 20}px;
            top: ${position.y - 10}px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 10000;
            pointer-events: none;
            max-width: 200px;
        ">
            ${tooltipContent}
        </div>
    `);

    $('body').append(tooltip);
}

// Hide quick tooltip
function hideQuickTooltip() {
    $('.quick-tooltip').remove();
}

// Render structure in a specific node
function renderStructureInNode(node, smiles) {
    try {
        const nodeId = node.id();
        const position = node.renderedPosition();
        const size = getEnhancedNodeSize(node.data('type'));

        // Create a unique canvas for this node
        const canvasId = `structure-canvas-${nodeId}`;
        let canvas = document.getElementById(canvasId);

        if (!canvas) {
            canvas = document.createElement('canvas');
            canvas.id = canvasId;
            canvas.width = size.width - 20; // Leave some padding
            canvas.height = size.height - 20;
            canvas.style.position = 'absolute';
            canvas.style.pointerEvents = 'none';
            canvas.style.zIndex = '1000';
            document.body.appendChild(canvas);
        }

        // Position canvas over the node
        const cyContainer = document.getElementById('cy');
        const containerRect = cyContainer.getBoundingClientRect();
        canvas.style.left = (containerRect.left + position.x - canvas.width / 2) + 'px';
        canvas.style.top = (containerRect.top + position.y - canvas.height / 2) + 'px';

        // Parse and draw the molecule
        SmilesDrawer.parse(smiles, function(tree) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Create a temporary drawer with smaller size
            const nodeDrawer = new SmilesDrawer.Drawer({
                width: canvas.width,
                height: canvas.height,
                bondThickness: 1.5,
                bondLength: 12,
                atomVisualization: 'default',
                terminalCarbons: false,
                explicitHydrogens: false,
                compactDrawing: true,
                fontSizeLarge: 8,
                fontSizeSmall: 6,
                padding: 5
            });

            // Draw to canvas
            nodeDrawer.draw(tree, canvas, 'light', false);
        }, function(err) {
            console.warn(`Failed to parse SMILES ${smiles}:`, err);
        });

        // Update canvas position when node moves
        node.on('position', function() {
            const newPosition = node.renderedPosition();
            canvas.style.left = (containerRect.left + newPosition.x - canvas.width / 2) + 'px';
            canvas.style.top = (containerRect.top + newPosition.y - canvas.height / 2) + 'px';
        });

    } catch (error) {
        console.error(`Error rendering structure for node ${node.id()}:`, error);
    }
}

// Highlight node
function highlightNode(node) {
    node.addClass('highlighted');

    // Animate the node
    node.animate({
        style: {
            'width': function(ele) {
                const size = getEnhancedNodeSize(ele.data('type'));
                return size.width * 1.1;
            },
            'height': function(ele) {
                const size = getEnhancedNodeSize(ele.data('type'));
                return size.height * 1.1;
            }
        }
    }, {
        duration: 200,
        easing: 'ease-out'
    });
}

// Unhighlight node
function unhighlightNode(node) {
    node.removeClass('highlighted');

    // Animate back to original size
    node.animate({
        style: {
            'width': function(ele) {
                const size = getEnhancedNodeSize(ele.data('type'));
                return size.width;
            },
            'height': function(ele) {
                const size = getEnhancedNodeSize(ele.data('type'));
                return size.height;
            }
        }
    }, {
        duration: 200,
        easing: 'ease-out'
    });
}

// Show molecular node details
function showMolecularNodeDetails(node) {
    const data = node.data();
    console.log("Molecular node clicked:", data);

    // Create detailed popup with molecular structure and activity data
    const popup = createMolecularPopup(data);
    showPopup(popup, node.renderedPosition());
}

// Create molecular popup with enhanced information
function createMolecularPopup(nodeData) {
    const popupId = `popup-${nodeData.id}`;

    let content = `
        <div id="${popupId}" class="molecular-popup">
            <div class="popup-header">
                <h5>${nodeData.name}</h5>
                <span class="node-type-badge ${nodeData.type}">${nodeData.type.toUpperCase()}</span>
                <button class="popup-close" onclick="closePopup('${popupId}')">&times;</button>
            </div>
            <div class="popup-body">
    `;

    switch (nodeData.type) {
        case 'compound':
            content += `
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-section">
                            <h6>Chemical Information</h6>
                            <p><strong>SMILES:</strong> <code class="smiles-code">${nodeData.smiles}</code></p>
                            <p><strong>Molecular Weight:</strong> <span id="mw-${nodeData.id}">Calculating...</span></p>
                        </div>

                        <div class="info-section">
                            <h6>Activity Data</h6>
                            <p><strong>Activity:</strong> <span class="activity-value">${nodeData.activity} ${nodeData.activity_unit}</span></p>
                            ${nodeData.pIC50 ? `<p><strong>pIC50:</strong> <span class="pic50-value">${nodeData.pIC50.toFixed(2)}</span></p>` : ''}
                            <p><strong>Lipinski Violations:</strong>
                                <span class="lipinski-badge violations-${nodeData.lipinski_violations || 0}">
                                    ${nodeData.lipinski_violations || 0}
                                </span>
                            </p>
                        </div>

                        ${nodeData.rgroups && nodeData.rgroups.length > 0 ? `
                        <div class="info-section">
                            <h6>R-Groups (${nodeData.rgroups.length})</h6>
                            <div class="rgroups-list">
                                ${nodeData.rgroups.map((rgroup, idx) => `
                                    <div class="rgroup-item">
                                        <strong>R${idx + 1}:</strong> <code>${rgroup.smiles}</code>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        ` : ''}
                    </div>
                    <div class="col-md-6">
                        <div class="structure-section">
                            <h6>Molecular Structure</h6>
                            <div class="large-structure-display" id="popup-structure-${nodeData.id}"></div>
                        </div>
                    </div>
                </div>
            `;
            break;

        case 'core':
            content += `
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-section">
                            <h6>Core Information</h6>
                            <p><strong>SMILES:</strong> <code class="smiles-code">${nodeData.smiles}</code></p>
                            <p><strong>Compounds:</strong> <span class="compound-count">${nodeData.count || 0}</span></p>
                        </div>

                        <div class="info-section">
                            <h6>Statistics</h6>
                            <p><strong>Core Type:</strong> Maximum Common Substructure</p>
                            <p><strong>Detection:</strong> Automatic clustering</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="structure-section">
                            <h6>Core Structure</h6>
                            <div class="large-structure-display" id="popup-structure-${nodeData.id}"></div>
                        </div>
                    </div>
                </div>
            `;
            break;

        case 'rgroup':
        case 'fragment':
            content += `
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-section">
                            <h6>${nodeData.type === 'rgroup' ? 'R-Group' : 'Fragment'} Information</h6>
                            <p><strong>SMILES:</strong> <code class="smiles-code">${nodeData.smiles}</code></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="structure-section">
                            <h6>Structure</h6>
                            <div class="large-structure-display" id="popup-structure-${nodeData.id}"></div>
                        </div>
                    </div>
                </div>
            `;
            break;
    }

    content += `
            </div>
        </div>
    `;

    return content;
}

// Create enhanced popup with molecular structure
function createEnhancedPopup(nodeData) {
    const popupId = `popup-${nodeData.id}`;

    let content = `
        <div id="${popupId}" class="enhanced-popup">
            <div class="popup-header">
                <h5>${nodeData.name}</h5>
                <button class="popup-close" onclick="closePopup('${popupId}')">&times;</button>
            </div>
            <div class="popup-body">
    `;

    switch (nodeData.type) {
        case 'compound':
            content += `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>SMILES:</strong> <code>${nodeData.smiles}</code></p>
                        <p><strong>Activity:</strong> ${nodeData.activity} ${nodeData.activity_unit}</p>
                        <p><strong>pIC50:</strong> ${nodeData.pIC50?.toFixed(2) || 'N/A'}</p>
                        <p><strong>Lipinski Violations:</strong> ${nodeData.lipinski_violations}</p>
                    </div>
                    <div class="col-md-6">
                        <div class="structure-display" id="popup-structure-${nodeData.id}"></div>
                    </div>
                </div>
            `;
            break;

        case 'scaffold':
            content += `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>SMILES:</strong> <code>${nodeData.smiles}</code></p>
                        <p><strong>Compounds:</strong> ${nodeData.count}</p>
                    </div>
                    <div class="col-md-6">
                        <div class="structure-display" id="popup-structure-${nodeData.id}"></div>
                    </div>
                </div>
            `;
            break;

        case 'fragment':
            content += `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>SMILES:</strong> <code>${nodeData.smiles}</code></p>
                    </div>
                    <div class="col-md-6">
                        <div class="structure-display" id="popup-structure-${nodeData.id}"></div>
                    </div>
                </div>
            `;
            break;
    }

    content += `
            </div>
        </div>
    `;

    return content;
}

// Show popup at position
function showPopup(content, position) {
    // Remove existing popups
    $('.enhanced-popup, .molecular-popup').remove();

    // Add popup to body
    $('body').append(content);

    // Position popup
    const popup = $('.enhanced-popup, .molecular-popup').last();
    popup.css({
        position: 'absolute',
        left: Math.min(position.x + 20, window.innerWidth - popup.outerWidth() - 20),
        top: Math.max(20, position.y - popup.outerHeight() / 2),
        zIndex: 10000
    });

    // Add fade-in animation
    popup.addClass('fade-in');

    // Render structure in popup
    const nodeId = popup.attr('id').replace('popup-', '');
    const structureId = `popup-structure-${nodeId}`;

    // Find the node data
    const node = enhancedCy.getElementById(nodeId);
    if (node.length > 0) {
        const data = node.data();
        const smiles = data.smiles;
        if (smiles) {
            renderLargeStructure(smiles, structureId);

            // Calculate molecular weight if possible
            if (typeof RDKit !== 'undefined' && window.RDKit) {
                try {
                    const mol = RDKit.Molecule.fromSmiles(smiles);
                    if (mol) {
                        const mw = mol.getMolWt();
                        $(`#mw-${nodeId}`).text(`${mw.toFixed(2)} g/mol`);
                        mol.delete();
                    }
                } catch (e) {
                    $(`#mw-${nodeId}`).text('N/A');
                }
            } else {
                $(`#mw-${nodeId}`).text('N/A');
            }
        }
    }
}

// Render large structure for popup
function renderLargeStructure(smiles, containerId) {
    if (!smiles) return;

    try {
        // Try RDKit first for better quality
        if (typeof RDKit !== 'undefined' && window.RDKit) {
            try {
                const mol = RDKit.Molecule.fromSmiles(smiles);
                if (mol) {
                    const svg = mol.toSVG(350, 250);
                    $(`#${containerId}`).html(svg);
                    mol.delete();
                    return;
                }
            } catch (rdkitError) {
                console.warn("RDKit large structure rendering failed:", rdkitError);
            }
        }

        // Fallback to SmilesDrawer
        if (typeof SmilesDrawer !== 'undefined') {
            SmilesDrawer.parse(smiles, function(tree) {
                const largeDrawer = new SmilesDrawer.Drawer({
                    width: 350,
                    height: 250,
                    bondThickness: 2.5,
                    bondLength: 18,
                    atomVisualization: 'default',
                    terminalCarbons: false,
                    explicitHydrogens: false,
                    compactDrawing: false,
                    fontSizeLarge: 14,
                    fontSizeSmall: 12,
                    padding: 15
                });

                largeDrawer.draw(tree, containerId, 'light', false);
            }, function(error) {
                console.warn("SmilesDrawer large structure parsing failed:", error);
                $(`#${containerId}`).html(`<p class="text-muted">Structure rendering failed</p>`);
            });
        } else {
            $(`#${containerId}`).html(`<p class="text-muted">No molecular rendering library available</p>`);
        }
    } catch (error) {
        console.error("Error rendering large structure:", error);
        $(`#${containerId}`).html(`<p class="text-danger">Error rendering structure</p>`);
    }
}

// Close popup
function closePopup(popupId) {
    $(`#${popupId}`).remove();
}

// Show error message
function showError(message) {
    $('#loading-indicator').addClass('d-none');
    $('#error-message').removeClass('d-none').text(message);
}

// Export functions for global access
window.createEnhancedVisualization = createEnhancedVisualization;
window.closePopup = closePopup;
