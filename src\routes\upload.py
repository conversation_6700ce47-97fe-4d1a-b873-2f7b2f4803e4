from flask import Blueprint, request, jsonify, current_app
import os
import pandas as pd
import uuid
import json
import math
from werkzeug.utils import secure_filename
from rdkit import Chem
from rdkit.Chem import AllChem

upload_bp = Blueprint('upload', __name__)

UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads')
ALLOWED_EXTENSIONS = {'csv', 'txt'}

# Create uploads directory if it doesn't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def validate_smiles(smiles):
    """Validate SMILES string using RDKit"""
    mol = Chem.MolFromSmiles(smiles)
    return mol is not None

def convert_activity(value, activity_type, target_type='pIC50'):
    """Convert between IC50 and pIC50 values"""
    try:
        value = float(value)
        if activity_type == target_type:
            return value
        
        if activity_type == 'IC50' and target_type == 'pIC50':
            # Convert IC50 (μM) to pIC50
            return -math.log10(value * 1e-6) if value > 0 else 0
        elif activity_type == 'pIC50' and target_type == 'IC50':
            # Convert pIC50 to IC50 (μM)
            return 10**(-value) * 1e6
        else:
            return value
    except (ValueError, TypeError):
        return None

def validate_csv_data(df):
    """Validate that the CSV contains required columns and data"""
    # Clean column names by stripping whitespace
    df.columns = [col.strip() for col in df.columns]
    
    required_columns = ['target', 'smiles', 'activity']
    errors = []
    
    # Check for required columns
    for col in required_columns:
        if col not in df.columns:
            errors.append(f"Missing required column: {col}")
    
    if errors:
        return False, errors, df
    
    # Clean data by stripping whitespace
    for col in df.columns:
        if df[col].dtype == 'object':
            df[col] = df[col].str.strip()
    
    # Validate SMILES strings
    invalid_smiles = []
    for idx, row in df.iterrows():
        if not validate_smiles(row['smiles']):
            invalid_smiles.append(f"Row {idx+1}: Invalid SMILES string: {row['smiles']}")
    
    if invalid_smiles:
        if len(invalid_smiles) > 10:
            errors.append(f"{len(invalid_smiles)} invalid SMILES strings found. First 10 shown:")
            errors.extend(invalid_smiles[:10])
        else:
            errors.append("Invalid SMILES strings found:")
            errors.extend(invalid_smiles)
    
    # Validate activity values (should be numeric)
    try:
        df['activity'] = pd.to_numeric(df['activity'])
    except ValueError:
        errors.append("Activity column contains non-numeric values")
    
    return len(errors) == 0, errors, df

@upload_bp.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and parse CSV data"""
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': 'No file part'}), 400
    
    file = request.files['file']
    activity_type = request.form.get('activity_type', 'pIC50')  # Default to pIC50
    
    if file.filename == '':
        return jsonify({'success': False, 'error': 'No selected file'}), 400
    
    if file and allowed_file(file.filename):
        # Secure the filename to prevent security issues
        filename = secure_filename(file.filename)
        
        # Generate a unique filename to prevent overwriting
        unique_filename = f"{uuid.uuid4()}_{filename}"
        file_path = os.path.join(UPLOAD_FOLDER, unique_filename)
        
        # Save the file
        file.save(file_path)
        
        try:
            # Parse the CSV file
            df = pd.read_csv(file_path)
            
            # Validate the data
            is_valid, errors, df = validate_csv_data(df)
            
            if not is_valid:
                # Remove the file if validation fails
                os.remove(file_path)
                return jsonify({
                    'success': False, 
                    'error': 'Invalid CSV data', 
                    'details': errors
                }), 400
            
            # Process the data
            compounds = []
            for _, row in df.iterrows():
                mol = Chem.MolFromSmiles(row['smiles'])
                if mol:
                    # Generate 2D coordinates for visualization
                    AllChem.Compute2DCoords(mol)
                    
                    # Convert activity to pIC50 if needed
                    original_activity = float(row['activity'])
                    pic50_value = convert_activity(original_activity, activity_type, 'pIC50')
                    ic50_value = convert_activity(original_activity, activity_type, 'IC50')
                    
                    # Convert molecule to JSON-compatible format
                    compound = {
                        'target': row['target'],
                        'smiles': row['smiles'],
                        'activity': original_activity,
                        'activity_type': activity_type,
                        'pIC50': pic50_value,
                        'IC50': ic50_value,
                        'svg': Chem.MolToMolBlock(mol)
                    }
                    compounds.append(compound)
            
            # Save processed data to a JSON file
            data_filename = f"{os.path.splitext(unique_filename)[0]}.json"
            data_path = os.path.join(UPLOAD_FOLDER, data_filename)
            
            with open(data_path, 'w') as f:
                json.dump({
                    'compounds': compounds,
                    'original_file': unique_filename,
                    'activity_type': activity_type
                }, f)
            
            return jsonify({
                'success': True,
                'message': 'File uploaded and processed successfully',
                'data_id': os.path.splitext(unique_filename)[0],
                'compound_count': len(compounds),
                'activity_type': activity_type
            })
            
        except Exception as e:
            # Remove the file if processing fails
            if os.path.exists(file_path):
                os.remove(file_path)
            return jsonify({'success': False, 'error': str(e)}), 500
    
    return jsonify({'success': False, 'error': 'File type not allowed'}), 400

@upload_bp.route('/uploads/<data_id>', methods=['GET'])
def get_processed_data(data_id):
    """Retrieve processed data by ID"""
    data_path = os.path.join(UPLOAD_FOLDER, f"{data_id}.json")
    
    if not os.path.exists(data_path):
        return jsonify({'success': False, 'error': 'Data not found'}), 404
    
    try:
        with open(data_path, 'r') as f:
            data = json.load(f)
        
        return jsonify({
            'success': True,
            'data': data
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@upload_bp.route('/sample', methods=['GET'])
def get_sample_csv():
    """Return a sample CSV template"""
    sample_data = "target,smiles,activity\n" + \
                 "HERG,COc1ccc(CC(=O)N)cc1,0.25\n" + \
                 "HERG,c1ccc2c(c1)ccc3ccccc32,1.5\n" + \
                 "HERG,CC(C)(C)c1ccc(O)cc1,0.75\n"
    
    return sample_data, 200, {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename=sample_compounds.csv'
    }
