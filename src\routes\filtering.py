from flask import Blueprint, request, jsonify
import os
import json
import math
import numpy as np

filtering_bp = Blueprint('filtering', __name__)

# Directory to store visualization data
DATA_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads')
os.makedirs(DATA_FOLDER, exist_ok=True)

def map_activity_to_color(pic50, min_pic50=5, max_pic50=9):
    """Map pIC50 value to a color gradient from blue (low activity) to red (high activity)"""
    # Normalize pIC50 to 0-1 range
    normalized = max(0, min(1, (pic50 - min_pic50) / (max_pic50 - min_pic50)))
    
    # Create color gradient: blue (low activity) -> white -> red (high activity)
    if normalized <= 0.5:
        # Blue to white
        r = int(255 * (normalized * 2))
        g = int(255 * (normalized * 2))
        b = 255
    else:
        # White to red
        r = 255
        g = int(255 * (1 - (normalized - 0.5) * 2))
        b = int(255 * (1 - (normalized - 0.5) * 2))
    
    return f"#{r:02x}{g:02x}{b:02x}"

@filtering_bp.route('/filter', methods=['POST'])
def filter_by_activity():
    """Filter visualization data by activity threshold"""
    viz_id = request.json.get('viz_id')
    threshold = float(request.json.get('threshold', 10))  # Default 10μM
    
    if not viz_id:
        return jsonify({'success': False, 'error': 'Visualization ID is required'}), 400
    
    # Check if visualization exists
    viz_path = os.path.join(DATA_FOLDER, f"{viz_id}.json")
    if not os.path.exists(viz_path):
        return jsonify({'success': False, 'error': 'Visualization not found'}), 404
    
    try:
        # Load the visualization
        with open(viz_path, 'r') as f:
            data = json.load(f)
        
        visualization = data.get('visualization', {})
        nodes = visualization.get('nodes', [])
        edges = visualization.get('edges', [])
        
        # Convert threshold from μM to pIC50
        pic50_threshold = -math.log10(threshold * 1e-6) if threshold > 0 else 6  # Default ~6
        
        # Find min and max pIC50 for color scaling
        pic50_values = [node.get('pIC50', 0) for node in nodes if node.get('type') == 'compound' and node.get('pIC50') is not None]
        min_pic50 = min(pic50_values) if pic50_values else 5
        max_pic50 = max(pic50_values) if pic50_values else 9
        
        # Apply color coding and filtering
        filtered_nodes = []
        for node in nodes:
            if node.get('type') == 'compound':
                pic50 = node.get('pIC50')
                
                if pic50 is not None:
                    # Apply color based on activity
                    node['color'] = map_activity_to_color(pic50, min_pic50, max_pic50)
                    
                    # Filter by threshold
                    if pic50 >= pic50_threshold:
                        filtered_nodes.append(node)
            else:
                filtered_nodes.append(node)
        
        # Update scaffold visibility based on remaining compounds
        scaffold_compounds = {}
        for node in filtered_nodes:
            if node.get('type') == 'compound':
                parent = node.get('parent')
                if parent:
                    scaffold_compounds[parent] = scaffold_compounds.get(parent, 0) + 1
        
        # Update scaffold nodes with compound counts
        for node in filtered_nodes:
            if node.get('type') == 'scaffold':
                node_id = node.get('id')
                if node_id in scaffold_compounds:
                    node['filtered_count'] = scaffold_compounds[node_id]
                else:
                    node['filtered_count'] = 0
        
        # Update edges for filtered nodes
        filtered_node_ids = [node.get('id') for node in filtered_nodes]
        filtered_edges = [edge for edge in edges if edge.get('source') in filtered_node_ids and edge.get('target') in filtered_node_ids]
        
        # Save filtered visualization
        filtered_viz_id = f"{viz_id}_filtered_{int(threshold)}"
        filtered_viz_path = os.path.join(DATA_FOLDER, f"{filtered_viz_id}.json")
        
        with open(filtered_viz_path, 'w') as f:
            json.dump({
                'visualization': {
                    'nodes': filtered_nodes,
                    'edges': filtered_edges
                },
                'original_viz_id': viz_id,
                'threshold': threshold,
                'pic50_threshold': pic50_threshold,
                'min_pic50': min_pic50,
                'max_pic50': max_pic50,
                'node_count': len(filtered_nodes),
                'edge_count': len(filtered_edges),
                'compound_count': len([n for n in filtered_nodes if n.get('type') == 'compound']),
                'scaffold_count': len([n for n in filtered_nodes if n.get('type') == 'scaffold'])
            }, f)
        
        return jsonify({
            'success': True,
            'message': f'Filtered visualization with threshold {threshold}μM (pIC50 {pic50_threshold:.2f})',
            'data_id': filtered_viz_id,
            'node_count': len(filtered_nodes),
            'edge_count': len(filtered_edges),
            'compound_count': len([n for n in filtered_nodes if n.get('type') == 'compound']),
            'scaffold_count': len([n for n in filtered_nodes if n.get('type') == 'scaffold'])
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@filtering_bp.route('/color-scheme', methods=['POST'])
def update_color_scheme():
    """Update color scheme for visualization"""
    viz_id = request.json.get('viz_id')
    color_scheme = request.json.get('color_scheme', 'activity')  # Default: activity
    
    if not viz_id:
        return jsonify({'success': False, 'error': 'Visualization ID is required'}), 400
    
    # Check if visualization exists
    viz_path = os.path.join(DATA_FOLDER, f"{viz_id}.json")
    if not os.path.exists(viz_path):
        return jsonify({'success': False, 'error': 'Visualization not found'}), 404
    
    try:
        # Load the visualization
        with open(viz_path, 'r') as f:
            data = json.load(f)
        
        visualization = data.get('visualization', {})
        nodes = visualization.get('nodes', [])
        
        # Find min and max values for color scaling
        if color_scheme == 'activity':
            values = [node.get('pIC50', 0) for node in nodes if node.get('type') == 'compound' and node.get('pIC50') is not None]
            min_val = min(values) if values else 5
            max_val = max(values) if values else 9
            
            # Apply color based on activity
            for node in nodes:
                if node.get('type') == 'compound':
                    pic50 = node.get('pIC50')
                    if pic50 is not None:
                        node['color'] = map_activity_to_color(pic50, min_val, max_val)
        
        elif color_scheme == 'lipinski':
            # Apply color based on Lipinski violations
            for node in nodes:
                if node.get('type') == 'compound':
                    violations = node.get('lipinski_violations', 0)
                    if violations == 0:
                        node['color'] = '#4caf50'  # Green
                    elif violations == 1:
                        node['color'] = '#ff9800'  # Orange
                    else:
                        node['color'] = '#f44336'  # Red
        
        # Save updated visualization
        updated_viz_id = f"{viz_id}_{color_scheme}"
        updated_viz_path = os.path.join(DATA_FOLDER, f"{updated_viz_id}.json")
        
        with open(updated_viz_path, 'w') as f:
            json.dump({
                'visualization': visualization,
                'original_viz_id': viz_id,
                'color_scheme': color_scheme
            }, f)
        
        return jsonify({
            'success': True,
            'message': f'Updated color scheme to {color_scheme}',
            'data_id': updated_viz_id
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
