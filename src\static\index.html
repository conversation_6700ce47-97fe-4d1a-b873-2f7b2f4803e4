<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChemMap - Chemical Structure Network Visualization</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="css/styles.css" rel="stylesheet">
    <link href="css/mcs-styles.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABLlJREFUWEetl1uoVVUUhj/NzCwrKkrsoKJmZXQwukBFdKcbRBcqpKQLRBHRk0UPFT1UPQRFD0EXiIqCLoQFJV3ooaKkMsvsYlmWZZZlZWXW+WbMxdprn73X3vvI3jDZa6815/jHmP8fc4raOyRdCJwP/AX8FP/+AH6XdLRNNbUttCTgBOAa4GrgCuCUCQr/DXwJbAE+lPRbG1lNBEi6CLgLuBM4uY2Qmmf+ATYD6yV93/R8IwGSzgIeAO4DTmwSMMNnDgMvA09L+qPu+VoCJJ0GPAY8CJw2g+Jrn/wReAx4R9KxqgdrCZD0FHBfDfP9wBfAHmAvcAD4HfhL0uEgTtIJwOnAOcD5wGJgGXBWjYxNwIOSDlTJGCNA0grgDeCMisT7gFeAtyX9WMWoqvskLQRuAe4GllQ89wewStKWsv9HBEhaALwJXFkh4ENgraTPm5g1PSfpMuBhYGXFcx8BqyUdKj5TJuAF4J6KhC8Bj0o61CSs7TNB0JPAAxVyHpf0ZPH5MQKS3gJuqkj2hKRH2iZv85ykp4CHKp5/W9LNxWdGBEh6Dlhd8fJaSevbJJ3lM2uBRyvkrJP0cP7ciABJW4HlJcH7gCWS/p1l8sbHJZ0IfA2cW/LgNkk35A+PCJC0D1hYEvyZpKsak87hAUnvAteVyNgvaVF+fyhA0lnAwZIXDwHnSfpnDnJbRUg6GfgJOKPkhbMl/TKMGR0BVwEflQS+IenOVmlzeEjSRuCOEtHXS/ooiR8REI7lfyXBiyX9MIf8rUWSLgZ2lzirvZIukHR0nICdwJKS4NWS1rdmnuODkh4HHi6Ru1PSsrEYIOlc4OeSF/cBF0v6e455W4eWOKP9wLklL50n6eeRABg5n1zQEUmntWad44OSjgPHl4g/IumUYQyQdCrwawnDTZLumGPe1uGS3gduLXlpk6Q7RwQEgdgMrCgJXiHp09bMc3xQ0nJge4ncLZJWjgiIFOxzYGlJ8BWSdsyRQOtQSVcDH5e8sFPSsjECgkRsA64tCV4q6ZvWGefw4Ih/KRe7TdL1YwQECXsAeLYk+XOSV80hf+uQkl4A7i15YZ2kh8YICBKxEdhQQiI+kLSyNescHpT0LnBjidiNkjaUEbAaeLmCRKyR9Mwc8rcOKWkNsKFE7GpJL5cRsBB4D7ikJPgocIekd1qzz/BBST8A55eI3QfcJOlAKQGRir0BrKogEd9KWibpyAzlND4u6bjIgKpS7wZJb1YSECRiEfA+cHZF0j3ADZIONkqZwUOSFkRFVVVm7wGWVzUmKgmIVOxC4FPgrIqk+4GbJe2egfzGRyVdCnwAnFnx4EFgqaS9VQ81EhCp2GLgE+D0GgHbgFsrOlkNIts9EgR0E3BbzXMHgCskfVf3fCMBkYqdA2wFLqgR8juwStKWdsibP5PVgJLuB9bXPLcXuFzST03PtxIQqdjpwEfA4gZhzwBrJP3ZJKzqGUlnRgVcVdpF1m+StFrSoTaymgmIVCxLsw+B0xoEHgEejTJtVlVx1GvnR7/g2gZZWZl9FXCZpF/byG4lIFKxrCDbBixtITQrz7ZEP3+HpKNRYGYl9iLgirjmXQYsaCErK7M3A6valmOtBOQkYj7wLXBqG8FznvlN0rw5y/k/KaP/AIQrFCZ+xrWrAAAAAElFTkSuQmCC">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-atom me-2"></i>
                ChemMap
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" aria-current="page" href="#" id="nav-home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="nav-upload">Upload</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="nav-database">Database</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="nav-visualization">Visualization</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="nav-freewilson">Free-Wilson</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        <!-- Home Section -->
        <div id="home-section" class="section active">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h4 class="card-title mb-0">Welcome to ChemMap</h4>
                        </div>
                        <div class="card-body">
                            <p class="lead">ChemMap is a tool for visualizing chemical structure networks and analyzing structure-activity relationships.</p>
                            
                            <div class="row mt-4">
                                <div class="col-md-4">
                                    <div class="card h-100">
                                        <div class="card-header bg-success text-white">
                                            <h5 class="card-title mb-0">Upload Data</h5>
                                        </div>
                                        <div class="card-body">
                                            <p>Upload your chemical structure data in CSV format.</p>
                                            <a href="#" class="btn btn-success" id="home-upload-btn">Upload Data</a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="card h-100">
                                        <div class="card-header bg-info text-white">
                                            <h5 class="card-title mb-0">Visualize Networks</h5>
                                        </div>
                                        <div class="card-body">
                                            <p>Visualize chemical structure networks with core detection.</p>
                                            <a href="#" class="btn btn-info" id="home-visualization-btn">Visualize Data</a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="card h-100">
                                        <div class="card-header bg-warning">
                                            <h5 class="card-title mb-0">Free-Wilson Analysis</h5>
                                        </div>
                                        <div class="card-body">
                                            <p>Perform Free-Wilson analysis on your data.</p>
                                            <a href="#" class="btn btn-warning" id="home-freewilson-btn">Free-Wilson Analysis</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Upload Section -->
        <div id="upload-section" class="section">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h4 class="card-title mb-0">Upload Data</h4>
                        </div>
                        <div class="card-body">
                            <form id="upload-form" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <label for="file-upload" class="form-label">Select CSV File</label>
                                    <input class="form-control" type="file" id="file-upload" accept=".csv">
                                    <div class="form-text">CSV file should contain SMILES and activity data.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="smiles-column" class="form-label">SMILES Column</label>
                                    <input type="text" class="form-control" id="smiles-column" placeholder="e.g., smiles, structure">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="activity-column" class="form-label">Activity Column</label>
                                    <input type="text" class="form-control" id="activity-column" placeholder="e.g., activity, ic50">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="activity-type" class="form-label">Activity Type</label>
                                    <select class="form-select" id="activity-type">
                                        <option value="ic50">IC50 (μM)</option>
                                        <option value="pic50">pIC50</option>
                                        <option value="ec50">EC50 (μM)</option>
                                        <option value="pec50">pEC50</option>
                                    </select>
                                </div>
                                
                                <button type="submit" class="btn btn-success" id="upload-btn">Upload</button>
                            </form>
                            
                            <div class="mt-3">
                                <div id="upload-progress" class="progress d-none">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div id="upload-message" class="alert d-none mt-3"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Database Section -->
        <div id="database-section" class="section">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h4 class="card-title mb-0">Database</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped" id="database-table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Compounds</th>
                                            <th>Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Database entries will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                            
                            <div id="database-message" class="alert d-none mt-3"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Visualization Section -->
        <div id="visualization-section" class="section">
            <div class="row">
                <div class="col-md-12 mb-3">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h4 class="card-title mb-0">Network Visualization</h4>
                        </div>
                        <div class="card-body">
                            <div class="controls-panel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="visualization-type" class="form-label">Visualization Type</label>
                                        <select class="form-select" id="visualization-type">
                                            <option value="hierarchical">Hierarchical (Scaffold-based)</option>
                                            <option value="core" selected>Core-based (MCS)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="activity-threshold" class="form-label">Activity Threshold</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="activity-threshold" value="10" min="0.001" max="1000" step="0.1">
                                            <span class="input-group-text">μM</span>
                                            <button class="btn btn-primary" id="apply-threshold-btn">Apply</button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <label class="form-label">Layout Options</label>
                                        <div class="layout-options">
                                            <span class="layout-option active" data-layout="cose">Force-directed</span>
                                            <span class="layout-option" data-layout="concentric">Concentric</span>
                                            <span class="layout-option" data-layout="grid">Grid</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Color Scheme</label>
                                        <div class="layout-options">
                                            <span class="layout-option active" data-color="activity">Activity</span>
                                            <span class="layout-option" data-color="lipinski">Lipinski</span>
                                            <span class="layout-option" data-color="type">Node Type</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="visualization-container"></div>
                            
                            <div id="loading-indicator" class="d-none">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span class="ms-2">Loading visualization...</span>
                            </div>
                            
                            <div id="error-message" class="alert alert-danger d-none"></div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div id="node-details" class="d-none"></div>
                </div>
                
                <div class="col-md-4">
                    <div id="visualization-info" class="d-none"></div>
                </div>
            </div>
        </div>
        
        <!-- Free-Wilson Section -->
        <div id="freewilson-section" class="section">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-warning">
                            <h4 class="card-title mb-0">Free-Wilson Analysis</h4>
                        </div>
                        <div class="card-body">
                            <form id="freewilson-form">
                                <div class="mb-3">
                                    <label for="freewilson-dataset" class="form-label">Select Dataset</label>
                                    <select class="form-select" id="freewilson-dataset">
                                        <!-- Datasets will be loaded here -->
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="freewilson-method" class="form-label">Analysis Method</label>
                                    <select class="form-select" id="freewilson-method">
                                        <option value="linear">Linear Regression</option>
                                        <option value="ridge">Ridge Regression</option>
                                        <option value="lasso">Lasso Regression</option>
                                    </select>
                                </div>
                                
                                <button type="submit" class="btn btn-warning" id="freewilson-btn">Run Analysis</button>
                            </form>
                            
                            <div class="mt-3">
                                <div id="freewilson-progress" class="progress d-none">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div id="freewilson-message" class="alert d-none mt-3"></div>
                            </div>
                            
                            <div id="freewilson-results" class="d-none mt-4"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <footer class="footer mt-5 py-3 bg-light">
        <div class="container">
            <span class="text-muted">ChemMap &copy; 2025</span>
        </div>
    </footer>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Cytoscape.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cytoscape/3.23.0/cytoscape.min.js"></script>
    
    <!-- RDKit.js -->
    <script src="https://unpkg.com/@rdkit/rdkit@2022.9.5/Code/MinimalLib/dist/RDKit_minimal.js"></script>
    
    <!-- SmilesDrawer.js -->
    <script src="https://unpkg.com/smiles-drawer@2.0.1/dist/smiles-drawer.min.js"></script>
    
    <!-- Custom JS -->
    <script src="js/upload.js"></script>
    <script src="js/database.js"></script>
    <script src="js/visualization.js"></script>
    <script src="js/visualization-mcs.js"></script>
    <script src="js/free-wilson.js"></script>
    
    <script>
        // Initialize RDKit
        window.onRDKitReady = function() {
            console.log('RDKit is ready');
        }
        
        // Navigation
        $(document).ready(function() {
            // Show active section
            $('.nav-link').on('click', function(e) {
                e.preventDefault();
                
                // Update active nav link
                $('.nav-link').removeClass('active');
                $(this).addClass('active');
                
                // Show corresponding section
                const targetId = $(this).attr('id').replace('nav-', '');
                $('.section').removeClass('active');
                $(`#${targetId}-section`).addClass('active');
            });
            
            // Home section buttons
            $('#home-upload-btn').on('click', function(e) {
                e.preventDefault();
                $('#nav-upload').click();
            });
            
            $('#home-visualization-btn').on('click', function(e) {
                e.preventDefault();
                $('#nav-visualization').click();
            });
            
            $('#home-freewilson-btn').on('click', function(e) {
                e.preventDefault();
                $('#nav-freewilson').click();
            });
            
            // Visualization type change
            $('#visualization-type').on('change', function() {
                const vizType = $(this).val();
                
                if (vizType === 'hierarchical') {
                    // Use hierarchical visualization
                    console.log('Switching to hierarchical visualization');
                    // Load current data with hierarchical visualization
                    const dataId = localStorage.getItem('currentDataId');
                    if (dataId) {
                        visualizeData(dataId);
                    }
                } else if (vizType === 'core') {
                    // Use core-based visualization
                    console.log('Switching to core-based visualization');
                    // Load current data with core-based visualization
                    const dataId = localStorage.getItem('currentDataId');
                    if (dataId) {
                        visualizeCoreData(dataId);
                    }
                }
            });
            
            // Layout options
            $('.layout-option').on('click', function() {
                const layoutGroup = $(this).parent();
                layoutGroup.find('.layout-option').removeClass('active');
                $(this).addClass('active');
                
                if (layoutGroup.parent().find('.form-label').text() === 'Layout Options') {
                    // Apply selected layout
                    const layoutName = $(this).data('layout');
                    applyLayout(layoutName);
                } else if (layoutGroup.parent().find('.form-label').text() === 'Color Scheme') {
                    // Apply selected color scheme
                    const colorScheme = $(this).data('color');
                    applyColorScheme(colorScheme);
                }
            });
            
            // Apply threshold button
            $('#apply-threshold-btn').on('click', function() {
                const threshold = parseFloat($('#activity-threshold').val());
                const dataId = localStorage.getItem('currentDataId');
                
                if (dataId && !isNaN(threshold)) {
                    const vizType = $('#visualization-type').val();
                    
                    if (vizType === 'hierarchical') {
                        visualizeData(dataId, threshold);
                    } else if (vizType === 'core') {
                        visualizeCoreData(dataId, threshold);
                    }
                }
            });
        });
        
        // Apply layout
        function applyLayout(layoutName) {
            if (!cy) return;
            
            let layout;
            
            switch (layoutName) {
                case 'cose':
                    layout = {
                        name: 'cose',
                        idealEdgeLength: 100,
                        nodeOverlap: 20,
                        refresh: 20,
                        fit: true,
                        padding: 30,
                        randomize: false,
                        componentSpacing: 100,
                        nodeRepulsion: 400000,
                        edgeElasticity: 100,
                        nestingFactor: 5,
                        gravity: 80,
                        numIter: 1000,
                        initialTemp: 200,
                        coolingFactor: 0.95,
                        minTemp: 1.0
                    };
                    break;
                    
                case 'concentric':
                    layout = {
                        name: 'concentric',
                        concentric: function(node) {
                            // Place cores in center, then compounds, then fragments/R-groups
                            if (node.data('type') === 'core') {
                                return 10;
                            } else if (node.data('type') === 'compound' || node.data('type') === 'scaffold') {
                                return 5;
                            } else {
                                return 1;
                            }
                        },
                        levelWidth: function() { return 1; },
                        minNodeSpacing: 50
                    };
                    break;
                    
                case 'grid':
                    layout = {
                        name: 'grid',
                        fit: true,
                        padding: 30,
                        avoidOverlap: true,
                        avoidOverlapPadding: 10,
                        rows: undefined,
                        columns: undefined
                    };
                    break;
                    
                default:
                    return;
            }
            
            cy.layout(layout).run();
        }
        
        // Apply color scheme
        function applyColorScheme(colorScheme) {
            if (!cy) return;
            
            cy.nodes().forEach(node => {
                const data = node.data();
                
                switch (colorScheme) {
                    case 'activity':
                        if (data.type === 'compound' && data.pIC50 !== undefined) {
                            // Map pIC50 to color (red to green)
                            const min = 5;  // Minimum pIC50
                            const max = 9;  // Maximum pIC50
                            const value = Math.max(min, Math.min(max, data.pIC50));
                            const ratio = (value - min) / (max - min);
                            
                            // Red (low activity) to Green (high activity)
                            const r = Math.round(255 * (1 - ratio));
                            const g = Math.round(255 * ratio);
                            const b = 0;
                            
                            node.style('background-color', `rgb(${r}, ${g}, ${b})`);
                        }
                        break;
                        
                    case 'lipinski':
                        if (data.type === 'compound') {
                            const violations = data.lipinski_violations || 0;
                            
                            if (violations === 0) {
                                node.style('background-color', '#4caf50');  // Green
                            } else if (violations === 1) {
                                node.style('background-color', '#ff9800');  // Orange
                            } else {
                                node.style('background-color', '#f44336');  // Red
                            }
                        }
                        break;
                        
                    case 'type':
                        // Use default type-based colors
                        if (data.type === 'core') {
                            node.style('background-color', '#FFD700');  // Gold
                        } else if (data.type === 'compound') {
                            node.style('background-color', '#A9A9A9');  // Dark gray
                        } else if (data.type === 'rgroup') {
                            node.style('background-color', '#87CEEB');  // Sky blue
                        } else if (data.type === 'scaffold') {
                            node.style('background-color', '#FFD700');  // Gold
                        } else if (data.type === 'fragment') {
                            node.style('background-color', '#87CEEB');  // Sky blue
                        }
                        break;
                }
            });
        }
    </script>
</body>
</html>
