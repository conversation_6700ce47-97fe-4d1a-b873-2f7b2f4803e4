import os
import json
import logging
from rdkit import Chem
from rdkit.Chem import AllChem, Draw, rdFMCS, rdRGroupDecomposition

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Data folder
DATA_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads')

def find_mcs(mols, threshold=0.7):
    """
    Find Maximum Common Substructure (MCS) among a set of molecules.
    
    Args:
        mols: List of RDKit molecule objects
        threshold: Minimum fraction of molecules that must contain the MCS
    
    Returns:
        RDKit molecule representing the MCS
    """
    if not mols or len(mols) < 2:
        return None
    
    # Calculate the MCS
    mcs_result = rdFMCS.FindMCS(
        mols,
        atomCompare=rdFMCS.AtomCompare.CompareElements,
        bondCompare=rdFMCS.BondCompare.CompareOrder,
        completeRingsOnly=True,
        threshold=threshold,
        timeout=60  # 60 seconds timeout
    )
    
    if mcs_result.numAtoms < 3:  # Require at least 3 atoms in MCS
        return None
    
    # Convert SMARTS to molecule
    mcs_mol = Chem.MolFromSmarts(mcs_result.smartsString)
    if mcs_mol is None:
        return None
    
    # Try to convert to a valid molecule
    try:
        mcs_mol = Chem.MolFromSmiles(Chem.MolToSmiles(mcs_mol, isomericSmiles=True))
        return mcs_mol
    except:
        return None

def cluster_by_similarity(mols, smiles_list, similarity_threshold=0.7):
    """
    Cluster molecules by structural similarity to identify potential multiple cores.
    
    Args:
        mols: List of RDKit molecule objects
        smiles_list: List of SMILES strings corresponding to mols
        similarity_threshold: Minimum Tanimoto similarity for clustering
    
    Returns:
        List of clusters, where each cluster is a list of indices into the original mols list
    """
    if not mols or len(mols) < 2:
        return [[0]] if mols else []
    
    # Generate fingerprints for all molecules
    fps = [AllChem.GetMorganFingerprintAsBitVect(mol, 2, 1024) for mol in mols if mol is not None]
    
    # Initialize clusters
    clusters = []
    unassigned = set(range(len(mols)))
    
    while unassigned:
        # Take the first unassigned molecule as a new cluster seed
        seed_idx = next(iter(unassigned))
        seed_fp = fps[seed_idx]
        
        # Find all molecules similar to this seed
        cluster = [seed_idx]
        unassigned.remove(seed_idx)
        
        # Check remaining unassigned molecules
        remaining = list(unassigned)
        for idx in remaining:
            if mols[idx] is None:
                unassigned.remove(idx)
                continue
                
            # Calculate similarity
            similarity = DataStructs.TanimotoSimilarity(seed_fp, fps[idx])
            if similarity >= similarity_threshold:
                cluster.append(idx)
                unassigned.remove(idx)
        
        clusters.append(cluster)
    
    return clusters

def identify_cores_and_rgroups(compounds):
    """
    Identify core structures and R-groups from a set of compounds.
    
    Args:
        compounds: List of compound dictionaries with SMILES
    
    Returns:
        Dictionary with cores and compounds organized by core
    """
    # Extract molecules from compounds
    mols = []
    smiles_list = []
    valid_compounds = []
    
    for compound in compounds:
        smiles = compound.get('smiles')
        if not smiles:
            continue
            
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            continue
            
        mols.append(mol)
        smiles_list.append(smiles)
        valid_compounds.append(compound)
    
    if not mols:
        return {"cores": [], "compounds": []}
    
    # Import DataStructs here to avoid circular import
    from rdkit import DataStructs
    
    # Cluster compounds to identify potential multiple cores
    clusters = cluster_by_similarity(mols, smiles_list)
    
    # Process each cluster to find its core
    cores = []
    core_compounds = {}
    
    for cluster_idx, cluster in enumerate(clusters):
        cluster_mols = [mols[idx] for idx in cluster]
        cluster_compounds = [valid_compounds[idx] for idx in cluster]
        
        # Find MCS for this cluster
        core_mol = find_mcs(cluster_mols)
        
        if core_mol is None:
            continue
            
        core_smiles = Chem.MolToSmiles(core_mol)
        core_id = f"core_{cluster_idx+1}"
        
        # Generate SVG for core
        core_svg = Chem.MolToMolBlock(core_mol)
        
        # Store core information
        cores.append({
            "id": core_id,
            "smiles": core_smiles,
            "svg": core_svg,
            "type": "core",
            "name": f"Core {cluster_idx+1}",
            "count": len(cluster_compounds)
        })
        
        # Perform R-group decomposition for each compound in this cluster
        core_compounds[core_id] = []
        
        for compound_idx, compound in enumerate(cluster_compounds):
            mol = cluster_mols[compound_idx]
            
            # Perform R-group decomposition
            try:
                match = mol.GetSubstructMatch(core_mol)
                if not match:
                    continue
                    
                # Identify R-groups
                rgroups = []
                for atom_idx in range(mol.GetNumAtoms()):
                    if atom_idx not in match:
                        # This atom is part of an R-group
                        # Find connected component
                        rgroup_atoms = Chem.FindAtomEnvironmentOfRadiusN(mol, 10, atom_idx)
                        if rgroup_atoms:
                            rgroup_mol = Chem.PathToSubmol(mol, rgroup_atoms)
                            if rgroup_mol and rgroup_mol.GetNumAtoms() > 0:
                                try:
                                    rgroup_smiles = Chem.MolToSmiles(rgroup_mol)
                                    rgroup_svg = Chem.MolToMolBlock(rgroup_mol)
                                    
                                    rgroups.append({
                                        "smiles": rgroup_smiles,
                                        "svg": rgroup_svg
                                    })
                                except:
                                    pass
                
                # Add compound with R-groups to core
                compound_with_rgroups = compound.copy()
                compound_with_rgroups["rgroups"] = rgroups
                compound_with_rgroups["core_id"] = core_id
                core_compounds[core_id].append(compound_with_rgroups)
                
            except Exception as e:
                logger.error(f"R-group decomposition error: {e}")
                continue
    
    # Return cores and compounds organized by core
    return {
        "cores": cores,
        "compounds_by_core": core_compounds
    }

def generate_core_network(compounds):
    """
    Generate a network with cores, compounds, and R-groups.
    
    Args:
        compounds: List of compound dictionaries with SMILES
    
    Returns:
        Dictionary with nodes and edges for network visualization
    """
    # Identify cores and R-groups
    core_data = identify_cores_and_rgroups(compounds)
    cores = core_data["cores"]
    compounds_by_core = core_data["compounds_by_core"]
    
    # Create nodes and edges
    nodes = []
    edges = []
    
    # Add core nodes
    for core in cores:
        nodes.append({
            'id': core['id'],
            'type': 'core',
            'name': core['name'],
            'smiles': core['smiles'],
            'svg': core['svg'],
            'count': core['count'],
            'visible': True,
            'expanded': False,
            'has_children': True
        })
    
    # Add compound and R-group nodes, and edges
    for core_id, core_compounds in compounds_by_core.items():
        for idx, compound in enumerate(core_compounds):
            compound_id = f"{compound.get('id', f'compound_{idx}')}"
            
            # Add compound node
            smiles = compound.get('smiles', '')
            mol = Chem.MolFromSmiles(smiles) if smiles else None
            
            activity = compound.get('activity', 0)
            pic50 = compound.get('pIC50', 0)
            activity_type = compound.get('activity_type', 'pIC50')
            activity_unit = 'pIC50' if activity_type == 'pIC50' else 'μM'
            
            nodes.append({
                'id': compound_id,
                'type': 'compound',
                'name': compound.get('compound_name', f"Compound {idx+1}"),
                'smiles': smiles,
                'svg': compound.get('svg', Chem.MolToMolBlock(mol) if mol else ''),
                'activity': activity,
                'activity_type': activity_type,
                'activity_unit': activity_unit,
                'pIC50': pic50,
                'visible': False,
                'expanded': False,
                'has_children': True,
                'parent': core_id,
                'lipinski_violations': compound.get('lipinski_violations', 0)
            })
            
            # Connect core to compound
            edges.append({
                'source': core_id,
                'target': compound_id,
                'visible': False
            })
            
            # Add R-group nodes
            rgroups = compound.get('rgroups', [])
            for r_idx, rgroup in enumerate(rgroups):
                rgroup_id = f"rgroup_{compound_id}_{r_idx}"
                
                nodes.append({
                    'id': rgroup_id,
                    'type': 'rgroup',
                    'name': f"R{r_idx+1}",
                    'smiles': rgroup.get('smiles', ''),
                    'svg': rgroup.get('svg', ''),
                    'visible': False,
                    'expanded': False,
                    'has_children': False,
                    'parent': compound_id
                })
                
                # Connect compound to R-group
                edges.append({
                    'source': compound_id,
                    'target': rgroup_id,
                    'visible': False
                })
    
    return {
        'nodes': nodes,
        'edges': edges,
        'cores': [{'id': core['id'], 'smiles': core['smiles'], 'count': core['count']} for core in cores]
    }
