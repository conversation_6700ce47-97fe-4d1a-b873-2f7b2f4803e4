// Database query functionality
$(document).ready(function() {
    // Check database connection on page load
    $.ajax({
        url: '/api/database/connect',
        type: 'GET',
        success: function(response) {
            if (response.success) {
                $('#db-connection-status').html(`
                    <div class="alert alert-success mb-3">
                        <i class="bi bi-check-circle"></i> ${response.message}
                    </div>
                `);
            } else {
                $('#db-connection-status').html(`
                    <div class="alert alert-warning mb-3">
                        <i class="bi bi-exclamation-triangle"></i> ${response.error}
                        <p>Database features will be disabled. Please use file upload instead.</p>
                    </div>
                `);
                $('#uniprot-search-form').find('input, button').prop('disabled', true);
            }
        },
        error: function() {
            $('#db-connection-status').html(`
                <div class="alert alert-warning mb-3">
                    <i class="bi bi-exclamation-triangle"></i> Could not connect to database
                    <p>Database features will be disabled. Please use file upload instead.</p>
                </div>
            `);
            $('#uniprot-search-form').find('input, button').prop('disabled', true);
        }
    });
    
    // Handle UniProt search form submission
    $('#uniprot-search-form').submit(function(e) {
        e.preventDefault();
        
        const uniprotId = $('#uniprot-id').val().trim();
        if (!uniprotId) {
            $('#db-search-result').html('<div class="alert alert-danger">Please enter a UniProt ID</div>');
            return;
        }
        
        const threshold = $('#db-activity-threshold').val();
        
        // Show progress
        $('#db-search-progress').removeClass('d-none');
        $('#db-search-progress .progress-bar').css('width', '0%');
        $('#db-search-result').html('');
        
        // Search compounds by UniProt ID
        $.ajax({
            url: `/api/database/search/uniprot?uniprot_id=${encodeURIComponent(uniprotId)}&threshold=${threshold}`,
            type: 'GET',
            xhr: function() {
                const xhr = new XMLHttpRequest();
                xhr.addEventListener('progress', function(e) {
                    if (e.lengthComputable) {
                        const percent = Math.round((e.loaded / e.total) * 100);
                        $('#db-search-progress .progress-bar').css('width', percent + '%');
                    }
                }, false);
                return xhr;
            },
            success: function(response) {
                $('#db-search-progress').addClass('d-none');
                
                if (response.success) {
                    $('#db-search-result').html(`
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle"></i> ${response.message}
                        </div>
                    `);
                    
                    // Store data ID for processing
                    $('#uniprot-search-form').data('dataId', response.data_id);
                    
                    // Show processing options
                    $('#db-processing-options').removeClass('d-none');
                } else {
                    $('#db-search-result').html(`
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i> ${response.error}
                        </div>
                    `);
                }
            },
            error: function(xhr) {
                $('#db-search-progress').addClass('d-none');
                
                try {
                    const response = JSON.parse(xhr.responseText);
                    $('#db-search-result').html(`
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i> ${response.error || 'Search failed'}
                        </div>
                    `);
                } catch (e) {
                    $('#db-search-result').html(`
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i> Search failed: ${xhr.status} ${xhr.statusText}
                        </div>
                    `);
                }
            }
        });
    });
    
    // Handle process database data button click
    $('#process-db-data').click(function() {
        const dataId = $('#uniprot-search-form').data('dataId');
        if (!dataId) {
            $('#db-search-result').html('<div class="alert alert-danger">No data available for processing</div>');
            return;
        }
        
        // Show progress
        $('#db-search-result').html(`
            <div class="alert alert-info">
                <i class="bi bi-gear-fill"></i> Processing data...
                <div class="spinner-border spinner-border-sm" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        `);
        
        // Step 1: Standardize compounds
        $.ajax({
            url: '/api/standardize/standardize',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ data_id: dataId }),
            success: function(response) {
                if (response.success) {
                    const stdDataId = response.data_id;
                    
                    // Step 2: Check Lipinski rules
                    $.ajax({
                        url: '/api/standardize/lipinski',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ data_id: stdDataId }),
                        success: function(response) {
                            if (response.success) {
                                // Step 3: Create hierarchical visualization
                                $.ajax({
                                    url: '/api/visualization/hierarchical',
                                    type: 'POST',
                                    contentType: 'application/json',
                                    data: JSON.stringify({ data_id: stdDataId }),
                                    success: function(response) {
                                        if (response.success) {
                                            const vizId = response.data_id;
                                            const threshold = $('#db-activity-threshold').val();
                                            
                                            // Step 4: Apply activity filtering
                                            $.ajax({
                                                url: '/api/filter/filter',
                                                type: 'POST',
                                                contentType: 'application/json',
                                                data: JSON.stringify({ 
                                                    viz_id: vizId,
                                                    threshold: threshold
                                                }),
                                                success: function(response) {
                                                    if (response.success) {
                                                        const filteredVizId = response.data_id;
                                                        
                                                        $('#db-search-result').html(`
                                                            <div class="alert alert-success">
                                                                <i class="bi bi-check-circle"></i> Data processed successfully
                                                                <p>Found ${response.compound_count} compounds and ${response.scaffold_count} scaffolds</p>
                                                            </div>
                                                        `);
                                                        
                                                        // Store visualization ID for later use
                                                        sessionStorage.setItem('currentVizId', filteredVizId);
                                                        
                                                        // Add to Free Wilson data source dropdown
                                                        const uniprotId = $('#uniprot-id').val().trim();
                                                        const option = `<option value="${stdDataId}">ChEMBL data for ${uniprotId} (${response.compound_count} compounds)</option>`;
                                                        $('#fw-data-source').append(option);
                                                        
                                                        // Show button to view visualization
                                                        $('#db-search-result').append(`
                                                            <button class="btn btn-primary mt-2" onclick="switchToTab('visualization-tab'); loadVisualization('${filteredVizId}');">
                                                                <i class="bi bi-diagram-3"></i> View Visualization
                                                            </button>
                                                        `);
                                                    } else {
                                                        $('#db-search-result').html(`
                                                            <div class="alert alert-danger">
                                                                <i class="bi bi-exclamation-triangle"></i> Filtering failed: ${response.error}
                                                            </div>
                                                        `);
                                                    }
                                                },
                                                error: handleAjaxError
                                            });
                                        } else {
                                            $('#db-search-result').html(`
                                                <div class="alert alert-danger">
                                                    <i class="bi bi-exclamation-triangle"></i> Visualization failed: ${response.error}
                                                </div>
                                            `);
                                        }
                                    },
                                    error: handleAjaxError
                                });
                            } else {
                                $('#db-search-result').html(`
                                    <div class="alert alert-danger">
                                        <i class="bi bi-exclamation-triangle"></i> Lipinski check failed: ${response.error}
                                    </div>
                                `);
                            }
                        },
                        error: handleAjaxError
                    });
                } else {
                    $('#db-search-result').html(`
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i> Standardization failed: ${response.error}
                        </div>
                    `);
                }
            },
            error: handleAjaxError
        });
    });
    
    // Helper function to handle AJAX errors
    function handleAjaxError(xhr) {
        try {
            const response = JSON.parse(xhr.responseText);
            $('#db-search-result').html(`
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> ${response.error || 'Request failed'}
                </div>
            `);
        } catch (e) {
            $('#db-search-result').html(`
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> Request failed: ${xhr.status} ${xhr.statusText}
                </div>
            `);
        }
    }
});
