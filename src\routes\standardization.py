from flask import Blueprint, request, jsonify
import os
import json
import math
import pandas as pd
from rdkit import Chem
from rdkit.Chem import AllChem, MolStandardize

standardization_bp = Blueprint('standardization', __name__)

# Directory to store standardized data
STANDARDIZED_DATA_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads')
os.makedirs(STANDARDIZED_DATA_FOLDER, exist_ok=True)

def standardize_molecule(smiles):
    """Standardize a molecule using RDKit's MolStandardize"""
    try:
        # Parse SMILES
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return None, "Invalid SMILES"
        
        # Remove fragments (keep largest fragment)
        largest_fragment = MolStandardize.fragment.LargestFragmentChooser()
        mol = largest_fragment.choose(mol)
        
        # Normalize the molecule
        normalizer = MolStandardize.normalize.Normalizer()
        mol = normalizer.normalize(mol)
        
        # Neutralize charges
        uncharger = MolStandardize.charge.Uncharger()
        mol = uncharger.uncharge(mol)
        
        # Generate 2D coordinates
        AllChem.Compute2DCoords(mol)
        
        # Return standardized SMILES
        return mol, Chem.MolToSmiles(mol)
    except Exception as e:
        return None, str(e)

def standardize_activity_to_pic50(activity_value, activity_type, activity_unit):
    """Convert activity values to pIC50 standard"""
    try:
        value = float(activity_value)
        
        # Convert to M (molar) if needed
        if activity_unit == 'nM':
            value = value * 1e-9
        elif activity_unit in ['μM', 'uM']:
            value = value * 1e-6
        elif activity_unit == 'mM':
            value = value * 1e-3
        
        # Convert to pIC50 = -log10(IC50)
        if activity_type in ['IC50', 'EC50', 'Ki', 'Kd']:
            return -1 * (math.log10(value) if value > 0 else 0)
        elif activity_type == 'pIC50':
            return value
        else:
            return None  # Unsupported activity type
    except Exception as e:
        print(f"Error standardizing activity: {e}")
        return None

@standardization_bp.route('/standardize', methods=['POST'])
def standardize_data():
    """Standardize compound data from either file upload or database query"""
    data_id = request.json.get('data_id')
    
    if not data_id:
        return jsonify({'success': False, 'error': 'Data ID is required'}), 400
    
    # Check if data exists
    data_path = os.path.join(STANDARDIZED_DATA_FOLDER, f"{data_id}.json")
    if not os.path.exists(data_path):
        return jsonify({'success': False, 'error': 'Data not found'}), 404
    
    try:
        # Load the data
        with open(data_path, 'r') as f:
            data = json.load(f)
        
        compounds = data.get('compounds', [])
        standardized_compounds = []
        
        for compound in compounds:
            smiles = compound.get('smiles')
            
            # Standardize molecule
            mol, std_smiles = standardize_molecule(smiles)
            
            if mol is None:
                continue  # Skip invalid molecules
            
            # Standardize activity to pIC50
            activity_value = compound.get('activity_value', compound.get('activity'))
            activity_type = compound.get('activity_type', 'IC50')  # Default to IC50
            activity_unit = compound.get('activity_unit', 'μM')    # Default to μM
            
            pic50 = compound.get('pIC50')
            if pic50 is None:
                pic50 = standardize_activity_to_pic50(activity_value, activity_type, activity_unit)
            
            # Create standardized compound
            std_compound = {
                'target': compound.get('target'),
                'smiles': std_smiles,
                'original_smiles': smiles,
                'activity': float(activity_value) if activity_value is not None else None,
                'activity_type': activity_type,
                'activity_unit': activity_unit,
                'pIC50': pic50,
                'mol_weight': Chem.Descriptors.MolWt(mol),
                'logp': Chem.Descriptors.MolLogP(mol),
                'h_donors': Chem.Descriptors.NumHDonors(mol),
                'h_acceptors': Chem.Descriptors.NumHAcceptors(mol),
                'rot_bonds': Chem.Descriptors.NumRotatableBonds(mol),
                'tpsa': Chem.Descriptors.TPSA(mol),
                'svg': Chem.MolToMolBlock(mol)
            }
            
            # Add additional fields if they exist in the original compound
            for key in ['chembl_id', 'compound_name', 'uniprot_id']:
                if key in compound:
                    std_compound[key] = compound[key]
            
            standardized_compounds.append(std_compound)
        
        # Save standardized data
        std_data_id = f"{data_id}_standardized"
        std_data_path = os.path.join(STANDARDIZED_DATA_FOLDER, f"{std_data_id}.json")
        
        with open(std_data_path, 'w') as f:
            json.dump({
                'compounds': standardized_compounds,
                'original_data_id': data_id,
                'compound_count': len(standardized_compounds)
            }, f)
        
        return jsonify({
            'success': True,
            'message': f'Standardized {len(standardized_compounds)} compounds',
            'data_id': std_data_id,
            'compound_count': len(standardized_compounds),
            'original_count': len(compounds),
            'filtered_count': len(compounds) - len(standardized_compounds)
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@standardization_bp.route('/lipinski', methods=['POST'])
def check_lipinski():
    """Check Lipinski's Rule of Five for compounds"""
    data_id = request.json.get('data_id')
    
    if not data_id:
        return jsonify({'success': False, 'error': 'Data ID is required'}), 400
    
    # Check if data exists
    data_path = os.path.join(STANDARDIZED_DATA_FOLDER, f"{data_id}.json")
    if not os.path.exists(data_path):
        return jsonify({'success': False, 'error': 'Data not found'}), 404
    
    try:
        # Load the data
        with open(data_path, 'r') as f:
            data = json.load(f)
        
        compounds = data.get('compounds', [])
        
        for compound in compounds:
            # Check Lipinski's Rule of Five
            violations = 0
            
            if compound.get('mol_weight', 0) > 500:
                violations += 1
            
            if compound.get('logp', 0) > 5:
                violations += 1
            
            if compound.get('h_donors', 0) > 5:
                violations += 1
            
            if compound.get('h_acceptors', 0) > 10:
                violations += 1
            
            compound['lipinski_violations'] = violations
        
        # Save updated data
        with open(data_path, 'w') as f:
            json.dump(data, f)
        
        return jsonify({
            'success': True,
            'message': f'Lipinski Rule of Five checked for {len(compounds)} compounds',
            'data_id': data_id
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
