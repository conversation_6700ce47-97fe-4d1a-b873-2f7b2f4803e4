/* Core-based Network Visualization Styles */

/* Visualization container */
#visualization-container {
    width: 100%;
    height: 600px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f8f9fa;
    margin-bottom: 20px;
}

/* Node details panel */
#node-details {
    margin-top: 20px;
    margin-bottom: 20px;
}

/* Structure containers */
.structure-container {
    width: 100%;
    height: 200px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: white;
    margin-top: 10px;
    padding: 10px;
    overflow: hidden;
}

/* Molecule tooltip */
#molecule-tooltip {
    position: absolute;
    z-index: 1000;
    width: 300px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    padding: 0;
    pointer-events: none;
}

.tooltip-header {
    padding: 8px 12px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    font-weight: bold;
    color: #333;
}

.tooltip-content {
    padding: 10px;
}

.tooltip-structure {
    width: 100%;
    height: 150px;
    background-color: white;
    margin-top: 10px;
}

/* Loading indicator */
#loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
}

/* Error message */
#error-message {
    margin-top: 20px;
    margin-bottom: 20px;
}

/* Core node styling */
.core-node {
    background-color: #FFD700 !important; /* Gold */
    border-color: #B8860B !important; /* Dark goldenrod */
    border-width: 3px !important;
    width: 80px !important;
    height: 80px !important;
}

/* Compound node styling */
.compound-node {
    background-color: #A9A9A9 !important; /* Dark gray */
    border-color: #4CAF50 !important; /* Green */
    border-width: 2px !important;
    width: 60px !important;
    height: 60px !important;
}

/* R-group node styling */
.rgroup-node {
    background-color: #87CEEB !important; /* Sky blue */
    border-color: #4682B4 !important; /* Steel blue */
    border-width: 2px !important;
    width: 40px !important;
    height: 40px !important;
}

/* Controls panel */
.controls-panel {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 5px;
}

/* Network layout options */
.layout-options {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.layout-option {
    padding: 5px 10px;
    background-color: #e9ecef;
    border: 1px solid #ced4da;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.layout-option:hover {
    background-color: #dee2e6;
}

.layout-option.active {
    background-color: #007bff;
    color: white;
    border-color: #0056b3;
}

/* Visualization info panel */
#visualization-info {
    margin-top: 20px;
}

/* Structure display enhancements */
svg.rdkit-molecule {
    max-width: 100%;
    max-height: 100%;
    margin: 0 auto;
    display: block;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #visualization-container {
        height: 400px;
    }
    
    .structure-container {
        height: 150px;
    }
    
    #molecule-tooltip {
        width: 250px;
    }
    
    .tooltip-structure {
        height: 120px;
    }
}

/* Animation for node expansion */
@keyframes nodeExpand {
    from { transform: scale(0.8); opacity: 0.5; }
    to { transform: scale(1); opacity: 1; }
}

.node-expanding {
    animation: nodeExpand 0.3s ease-out forwards;
}

/* Highlight for active core */
.active-core {
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
    transform: scale(1.1);
    transition: all 0.3s ease;
}

/* Network edge styling */
.core-edge {
    stroke: #B8860B !important;
    stroke-width: 3px !important;
}

.compound-edge {
    stroke: #4CAF50 !important;
    stroke-width: 2px !important;
}

.rgroup-edge {
    stroke: #4682B4 !important;
    stroke-width: 1.5px !important;
}
