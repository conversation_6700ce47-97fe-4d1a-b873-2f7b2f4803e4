// Visualization functionality
let cy; // Cytoscape instance

$(document).ready(function() {
    console.log("Visualization.js loaded");
    
    // Initialize visualization when tab is shown
    $('a[href="#visualization-tab"]').on('click', function(e) {
        console.log("Visualization tab clicked");
        const dataId = localStorage.getItem('currentDataId');
        if (dataId) {
            console.log("Found data ID:", dataId);
            createVisualization(dataId);
        } else {
            console.log("No data ID found in localStorage");
            $('#cy').html('<div class="alert alert-warning m-3">No data available. Please upload a file or query the database first.</div>');
        }
    });
    
    // Handle filter application
    $('#apply-filters').click(function() {
        const dataId = localStorage.getItem('currentDataId');
        if (!dataId) {
            alert('No visualization data available');
            return;
        }
        
        const threshold = $('#activity-threshold').val();
        const colorScheme = $('input[name="color-scheme"]:checked').val();
        
        // Show loading indicator
        $('#cy').html('<div class="d-flex justify-content-center align-items-center h-100"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');
        
        // Apply filters and update visualization
        createVisualization(dataId, threshold, colorScheme);
    });
    
    // Handle download buttons
    $('#download-svg').click(function() {
        if (!cy) return;
        
        const svgContent = cy.svg({ scale: 2, full: true });
        const blob = new Blob([svgContent], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'chemmap_visualization.svg';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    });
    
    $('#download-png').click(function() {
        if (!cy) return;
        
        const pngContent = cy.png({ scale: 2, full: true });
        
        const a = document.createElement('a');
        a.href = pngContent;
        a.download = 'chemmap_visualization.png';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    });
});

// Create hierarchical visualization
function createVisualization(dataId, threshold = 10, colorScheme = 'activity') {
    console.log("Creating visualization for data ID:", dataId);
    console.log("Threshold:", threshold, "Color scheme:", colorScheme);
    
    // Show loading indicator
    $('#cy').html('<div class="d-flex justify-content-center align-items-center h-100"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');
    
    // First, create the hierarchical visualization on the server
    $.ajax({
        url: '/api/visualization/hierarchical',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ 
            data_id: dataId,
            threshold: threshold
        }),
        success: function(response) {
            console.log("Hierarchical visualization created:", response);
            if (response.success) {
                const vizId = response.data_id;
                
                // Now load the visualization
                loadVisualization(vizId);
            } else {
                $('#cy').html(`<div class="alert alert-danger m-3">Failed to create visualization: ${response.error}</div>`);
                console.error("Visualization creation failed:", response.error);
            }
        },
        error: function(xhr, status, error) {
            console.error("AJAX error:", status, error);
            try {
                const response = JSON.parse(xhr.responseText);
                $('#cy').html(`<div class="alert alert-danger m-3">Failed to create visualization: ${response.error}</div>`);
            } catch (e) {
                $('#cy').html(`<div class="alert alert-danger m-3">Failed to create visualization: ${xhr.status} ${xhr.statusText}</div>`);
            }
        }
    });
}

// Load visualization data
function loadVisualization(vizId) {
    console.log("Loading visualization for viz ID:", vizId);
    
    // Get visualization data
    $.ajax({
        url: `/api/visualization/visualization/${vizId}`,
        type: 'GET',
        success: function(response) {
            console.log("Visualization data loaded:", response);
            if (response.success) {
                const data = response.data;
                const visualization = data.visualization;
                
                // Update session storage
                localStorage.setItem('currentVizId', vizId);
                
                // Initialize Cytoscape
                initCytoscape(visualization);
                
                // Update statistics
                updateStatistics(visualization);
            } else {
                $('#cy').html(`<div class="alert alert-danger m-3">Failed to load visualization: ${response.error}</div>`);
                console.error("Visualization loading failed:", response.error);
            }
        },
        error: function(xhr, status, error) {
            console.error("AJAX error:", status, error);
            try {
                const response = JSON.parse(xhr.responseText);
                $('#cy').html(`<div class="alert alert-danger m-3">Failed to load visualization: ${response.error}</div>`);
            } catch (e) {
                $('#cy').html(`<div class="alert alert-danger m-3">Failed to load visualization: ${xhr.status} ${xhr.statusText}</div>`);
            }
        }
    });
}

// Update network statistics
function updateStatistics(visualization) {
    const nodes = visualization.nodes;
    const compounds = nodes.filter(node => node.type === 'compound').length;
    const scaffolds = nodes.filter(node => node.type === 'scaffold').length;
    const fragments = nodes.filter(node => node.type === 'fragment').length;
    
    $('#compounds-count').text(compounds);
    $('#scaffolds-count').text(scaffolds);
    $('#fragments-count').text(fragments);
    $('#total-nodes-count').text(nodes.length);
    
    console.log("Statistics updated:", {
        compounds,
        scaffolds,
        fragments,
        totalNodes: nodes.length
    });
}

// Initialize Cytoscape visualization
function initCytoscape(visualization) {
    console.log("Initializing Cytoscape with visualization data:", visualization);
    
    if (!visualization || !visualization.nodes || !visualization.edges) {
        $('#cy').html('<div class="alert alert-danger m-3">Invalid visualization data</div>');
        console.error("Invalid visualization data:", visualization);
        return;
    }
    
    const nodes = visualization.nodes;
    const edges = visualization.edges;
    
    if (nodes.length === 0) {
        $('#cy').html('<div class="alert alert-warning m-3">No compounds found in the data</div>');
        console.warn("No nodes in visualization data");
        return;
    }
    
    console.log(`Initializing Cytoscape with ${nodes.length} nodes and ${edges.length} edges`);
    
    // Prepare Cytoscape elements
    const elements = {
        nodes: nodes.map(node => ({
            data: {
                id: node.id,
                type: node.type,
                name: node.name || node.id,
                smiles: node.smiles || '',
                svg: node.svg || '',
                activity: node.activity || 0,
                activity_unit: node.activity_unit || 'μM',
                pIC50: node.pIC50 || 0,
                color: node.color || getNodeColor(node),
                parent: node.parent || null,
                expanded: node.expanded || false,
                has_children: node.has_children || false,
                lipinski_violations: node.lipinski_violations || 0
            },
            position: getNodePosition(node, nodes),
            classes: node.type
        })),
        edges: edges.filter(edge => edge.visible).map(edge => ({
            data: {
                source: edge.source,
                target: edge.target
            }
        }))
    };
    
    console.log("Cytoscape elements prepared:", elements);
    
    // Initialize Cytoscape
    cy = cytoscape({
        container: document.getElementById('cy'),
        elements: elements,
        style: [
            {
                selector: 'node',
                style: {
                    'label': 'data(name)',
                    'text-valign': 'center',
                    'text-halign': 'center',
                    'background-color': 'data(color)',
                    'color': '#fff',
                    'text-outline-width': 1,
                    'text-outline-color': '#000',
                    'font-size': 12,
                    'width': function(ele) {
                        return getNodeSize(ele.data('type'));
                    },
                    'height': function(ele) {
                        return getNodeSize(ele.data('type'));
                    },
                    'border-width': function(ele) {
                        return ele.data('type') === 'compound' ? 3 : 1;
                    },
                    'border-color': function(ele) {
                        if (ele.data('type') === 'compound') {
                            const violations = ele.data('lipinski_violations');
                            if (violations === 0) return '#4caf50';
                            if (violations === 1) return '#ff9800';
                            return '#f44336';
                        }
                        return '#000';
                    }
                }
            },
            {
                selector: 'edge',
                style: {
                    'width': 2,
                    'line-color': '#ccc',
                    'curve-style': 'bezier'
                }
            },
            {
                selector: 'node.target',
                style: {
                    'background-color': '#3498db',
                    'shape': 'diamond'
                }
            },
            {
                selector: 'node.scaffold',
                style: {
                    'background-color': '#f39c12',
                    'shape': 'hexagon'
                }
            },
            {
                selector: 'node.compound',
                style: {
                    'shape': 'ellipse'
                }
            },
            {
                selector: 'node.fragment',
                style: {
                    'background-color': '#95a5a6',
                    'shape': 'round-rectangle',
                    'width': 30,
                    'height': 30
                }
            }
        ],
        layout: {
            name: 'cose',
            idealEdgeLength: 100,
            nodeOverlap: 20,
            refresh: 20,
            fit: true,
            padding: 30,
            randomize: false,
            componentSpacing: 100,
            nodeRepulsion: 400000,
            edgeElasticity: 100,
            nestingFactor: 5,
            gravity: 80,
            numIter: 1000,
            initialTemp: 200,
            coolingFactor: 0.95,
            minTemp: 1.0
        }
    });
    
    console.log("Cytoscape initialized");
    
    // Add event listeners
    cy.on('tap', 'node', function(evt) {
        const node = evt.target;
        showMoleculeTooltip(node);
    });
    
    cy.on('dblclick', 'node', function(evt) {
        const node = evt.target;
        toggleNodeExpansion(node);
    });
    
    // Fit the graph
    cy.fit();
    console.log("Graph fitted to viewport");
}

// Get node color based on type and properties
function getNodeColor(node) {
    switch (node.type) {
        case 'target':
            return '#3498db';
        case 'scaffold':
            return '#f39c12';
        case 'compound':
            // Color based on activity (pIC50)
            const pic50 = node.pIC50 || 0;
            if (pic50 <= 6) return '#3498db'; // Low activity (blue)
            if (pic50 >= 8) return '#e74c3c'; // High activity (red)
            // Gradient between blue and red
            const percent = (pic50 - 6) / 2; // 0 to 1 for pIC50 6-8
            return interpolateColor('#3498db', '#e74c3c', percent);
        case 'fragment':
            return '#95a5a6';
        default:
            return '#999';
    }
}

// Interpolate between two colors
function interpolateColor(color1, color2, percent) {
    // Convert hex to RGB
    const r1 = parseInt(color1.substring(1, 3), 16);
    const g1 = parseInt(color1.substring(3, 5), 16);
    const b1 = parseInt(color1.substring(5, 7), 16);
    
    const r2 = parseInt(color2.substring(1, 3), 16);
    const g2 = parseInt(color2.substring(3, 5), 16);
    const b2 = parseInt(color2.substring(5, 7), 16);
    
    // Interpolate
    const r = Math.round(r1 + (r2 - r1) * percent);
    const g = Math.round(g1 + (g2 - g1) * percent);
    const b = Math.round(b1 + (b2 - b1) * percent);
    
    // Convert back to hex
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}

// Get node size based on type
function getNodeSize(type) {
    switch (type) {
        case 'target':
            return 60;
        case 'scaffold':
            return 50;
        case 'compound':
            return 40;
        case 'fragment':
            return 30;
        default:
            return 30;
    }
}

// Get node position
function getNodePosition(node, allNodes) {
    if (node.x && node.y) {
        return { x: node.x, y: node.y };
    }
    
    // Default positions based on node type
    switch (node.type) {
        case 'target':
            return { x: 0, y: 0 };
        case 'scaffold':
            // Position scaffolds in a circle around the target
            const scaffoldCount = allNodes.filter(n => n.type === 'scaffold').length;
            const scaffoldIndex = allNodes.filter(n => n.type === 'scaffold' && n.id < node.id).length;
            const angle = (2 * Math.PI * scaffoldIndex) / scaffoldCount;
            return {
                x: 200 * Math.cos(angle),
                y: 200 * Math.sin(angle)
            };
        case 'compound':
            // Position compounds near their parent scaffold
            if (node.parent) {
                const parent = allNodes.find(n => n.id === node.parent);
                if (parent && parent.x && parent.y) {
                    const compoundCount = allNodes.filter(n => n.type === 'compound' && n.parent === node.parent).length;
                    const compoundIndex = allNodes.filter(n => n.type === 'compound' && n.parent === node.parent && n.id < node.id).length;
                    const angle = (2 * Math.PI * compoundIndex) / compoundCount;
                    return {
                        x: parent.x + 100 * Math.cos(angle),
                        y: parent.y + 100 * Math.sin(angle)
                    };
                }
            }
            return { x: Math.random() * 500, y: Math.random() * 500 };
        case 'fragment':
            // Position fragments near their parent compound
            if (node.parent) {
                const parent = allNodes.find(n => n.id === node.parent);
                if (parent && parent.x && parent.y) {
                    const fragmentCount = allNodes.filter(n => n.type === 'fragment' && n.parent === node.parent).length;
                    const fragmentIndex = allNodes.filter(n => n.type === 'fragment' && n.parent === node.parent && n.id < node.id).length;
                    const angle = (2 * Math.PI * fragmentIndex) / fragmentCount;
                    return {
                        x: parent.x + 50 * Math.cos(angle),
                        y: parent.y + 50 * Math.sin(angle)
                    };
                }
            }
            return { x: Math.random() * 500, y: Math.random() * 500 };
        default:
            return { x: Math.random() * 500, y: Math.random() * 500 };
    }
}

// Show molecule tooltip
function showMoleculeTooltip(node) {
    const data = node.data();
    console.log("Showing tooltip for node:", data);
    
    // Create tooltip if it doesn't exist
    if ($('#molecule-tooltip').length === 0) {
        $('body').append('<div id="molecule-tooltip" class="d-none"></div>');
    }
    
    // Position tooltip near the node
    const position = node.renderedPosition();
    const tooltip = $('#molecule-tooltip');
    
    tooltip.css({
        'left': position.x + 50,
        'top': position.y - 100
    }).removeClass('d-none');
    
    // Set tooltip content based on node type
    let content = '';
    
    switch (data.type) {
        case 'target':
            content = `
                <div class="tooltip-header">Target: ${data.name}</div>
                <div class="tooltip-content">
                    <p>Click on scaffolds to explore compounds</p>
                </div>
            `;
            break;
            
        case 'scaffold':
            content = `
                <div class="tooltip-header">Scaffold: ${data.name}</div>
                <div class="tooltip-content">
                    <p>SMILES: ${data.smiles}</p>
                    <div class="tooltip-structure" id="scaffold-structure"></div>
                </div>
            `;
            break;
            
        case 'compound':
            content = `
                <div class="tooltip-header">Compound: ${data.name}</div>
                <div class="tooltip-content">
                    <p>SMILES: ${data.smiles}</p>
                    <p>Activity: ${data.activity} ${data.activity_unit}</p>
                    <p>pIC50: ${data.pIC50.toFixed(2)}</p>
                    <div class="tooltip-structure" id="compound-structure"></div>
                </div>
            `;
            break;
            
        case 'fragment':
            content = `
                <div class="tooltip-header">Fragment: ${data.name}</div>
                <div class="tooltip-content">
                    <p>SMILES: ${data.smiles}</p>
                    <div class="tooltip-structure" id="fragment-structure"></div>
                </div>
            `;
            break;
    }
    
    tooltip.html(content);
    
    // Render molecule structure if available
    if (data.smiles && data.type !== 'target') {
        const structureId = `${data.type}-structure`;
        renderMolecule(data.smiles, data.svg, structureId);
    }
}

// Render molecule structure
function renderMolecule(smiles, molblock, containerId) {
    if (!smiles) return;
    
    try {
        // Use RDKit.js if available
        if (typeof RDKit !== 'undefined') {
            const mol = RDKit.Molecule.fromSmiles(smiles);
            const svg = mol.toSVG();
            $(`#${containerId}`).html(svg);
        } 
        // Fallback to molblock if available
        else if (molblock) {
            // Convert molblock to SVG using SmilesDrawer or other library
            $(`#${containerId}`).html(`<pre>${molblock}</pre>`);
        }
        // Fallback to just showing SMILES
        else {
            $(`#${containerId}`).html(`<p>${smiles}</p>`);
        }
    } catch (e) {
        console.error("Error rendering molecule:", e);
        $(`#${containerId}`).html(`<p>Error rendering structure</p>`);
    }
}

// Toggle node expansion
function toggleNodeExpansion(node) {
    const data = node.data();
    console.log("Toggling expansion for node:", data);
    
    if (!data.has_children) return;
    
    const expanded = !data.expanded;
    node.data('expanded', expanded);
    
    // Find child nodes
    const childNodes = cy.nodes().filter(n => n.data('parent') === data.id);
    
    // Toggle visibility of child nodes and their edges
    childNodes.forEach(childNode => {
        childNode.style('display', expanded ? 'element' : 'none');
        
        // Also toggle edges connected to this child
        cy.edges().filter(e => e.data('source') === childNode.id() || e.data('target') === childNode.id())
            .style('display', expanded ? 'element' : 'none');
    });
    
    // If expanding, also expand any already expanded children
    if (expanded) {
        childNodes.forEach(childNode => {
            if (childNode.data('expanded')) {
                toggleNodeExpansion(childNode);
            }
        });
    }
}

// Function to be called from upload.js after successful upload
function visualizeData(dataId, threshold = 10) {
    console.log("Visualizing data:", dataId, "with threshold:", threshold);
    localStorage.setItem('currentDataId', dataId);
    createVisualization(dataId, threshold);
}
