// Core-based Network Visualization
let cy = null;

// Initialize visualization
function initCoreVisualization() {
    console.log("Initializing core-based visualization");
    
    // Create cytoscape instance if it doesn't exist
    if (!cy) {
        cy = cytoscape({
            container: document.getElementById('visualization-container'),
            style: [
                {
                    selector: 'node',
                    style: {
                        'background-color': '#666',
                        'label': 'data(name)',
                        'text-valign': 'center',
                        'text-halign': 'center',
                        'text-outline-width': 2,
                        'text-outline-color': '#fff',
                        'color': '#000',
                        'font-size': '12px',
                        'width': 'data(size)',
                        'height': 'data(size)',
                        'border-width': 2,
                        'border-color': '#000'
                    }
                },
                {
                    selector: 'node[type="core"]',
                    style: {
                        'background-color': '#FFD700', // Gold
                        'border-color': '#B8860B', // Dark goldenrod
                        'border-width': 3,
                        'font-weight': 'bold',
                        'font-size': '14px',
                        'width': 80,
                        'height': 80,
                        'text-outline-width': 3,
                        'text-outline-color': '#fff',
                        'color': '#000'
                    }
                },
                {
                    selector: 'node[type="compound"]',
                    style: {
                        'background-color': '#A9A9A9', // Dark gray
                        'border-color': '#4CAF50', // Green
                        'border-width': 2,
                        'width': 60,
                        'height': 60
                    }
                },
                {
                    selector: 'node[type="rgroup"]',
                    style: {
                        'background-color': '#87CEEB', // Sky blue
                        'border-color': '#4682B4', // Steel blue
                        'border-width': 2,
                        'width': 40,
                        'height': 40
                    }
                },
                {
                    selector: 'edge',
                    style: {
                        'width': 2,
                        'line-color': '#ccc',
                        'curve-style': 'bezier'
                    }
                },
                {
                    selector: 'edge[source^="core"]',
                    style: {
                        'width': 3,
                        'line-color': '#B8860B' // Dark goldenrod
                    }
                },
                {
                    selector: '.highlighted',
                    style: {
                        'border-color': '#FF5733', // Highlight color
                        'border-width': 4
                    }
                },
                {
                    selector: 'node:selected',
                    style: {
                        'border-color': '#FF5733', // Highlight color
                        'border-width': 4
                    }
                }
            ],
            layout: {
                name: 'cose',
                idealEdgeLength: 100,
                nodeOverlap: 20,
                refresh: 20,
                fit: true,
                padding: 30,
                randomize: false,
                componentSpacing: 100,
                nodeRepulsion: 400000,
                edgeElasticity: 100,
                nestingFactor: 5,
                gravity: 80,
                numIter: 1000,
                initialTemp: 200,
                coolingFactor: 0.95,
                minTemp: 1.0
            }
        });
        
        // Register event handlers
        cy.on('tap', 'node', function(evt) {
            const node = evt.target;
            handleNodeClick(node);
        });
        
        cy.on('mouseover', 'node', function(evt) {
            const node = evt.target;
            showMoleculeTooltip(node);
        });
        
        cy.on('mouseout', 'node', function() {
            hideMoleculeTooltip();
        });
    }
    
    return cy;
}

// Load core-based visualization
function loadCoreVisualization(vizId) {
    console.log("Loading core-based visualization:", vizId);
    
    // Initialize visualization
    const cy = initCoreVisualization();
    
    // Clear existing elements
    cy.elements().remove();
    
    // Show loading indicator
    $('#loading-indicator').removeClass('d-none');
    
    // Fetch visualization data
    $.ajax({
        url: `/api/visualization/visualization/${vizId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const vizData = response.data.visualization;
                renderCoreNetwork(vizData);
                
                // Hide loading indicator
                $('#loading-indicator').addClass('d-none');
                
                // Update visualization info
                updateVisualizationInfo(response.data);
            } else {
                console.error("Error loading visualization:", response.error);
                $('#error-message').text(`Error: ${response.error}`).removeClass('d-none');
                $('#loading-indicator').addClass('d-none');
            }
        },
        error: function(xhr) {
            console.error("AJAX error:", xhr.responseText);
            $('#error-message').text(`Error: ${xhr.responseText || 'Failed to load visualization'}`).removeClass('d-none');
            $('#loading-indicator').addClass('d-none');
        }
    });
}

// Render core network
function renderCoreNetwork(networkData) {
    console.log("Rendering core network:", networkData);
    
    const nodes = networkData.nodes || [];
    const edges = networkData.edges || [];
    
    // Add nodes
    nodes.forEach(node => {
        // Set node size based on type
        let size = 60; // Default
        
        if (node.type === 'core') {
            size = 80;
        } else if (node.type === 'compound') {
            size = 60;
        } else if (node.type === 'rgroup') {
            size = 40;
        }
        
        // Add node to graph
        cy.add({
            group: 'nodes',
            data: {
                ...node,
                size: size
            },
            position: getInitialPosition(node)
        });
    });
    
    // Add edges
    edges.forEach(edge => {
        cy.add({
            group: 'edges',
            data: edge
        });
    });
    
    // Apply layout
    applyNetworkLayout();
    
    // Show only core nodes initially
    cy.nodes().forEach(node => {
        const data = node.data();
        
        if (data.type === 'core') {
            node.style('display', 'element');
        } else {
            node.style('display', 'none');
        }
    });
    
    // Hide all edges initially
    cy.edges().style('display', 'none');
}

// Get initial position for node
function getInitialPosition(node) {
    const type = node.type;
    const parent = node.parent;
    
    switch (type) {
        case 'core':
            // Position cores in a circle
            const coreCount = cy.nodes('[type="core"]').length;
            const angle = (2 * Math.PI * coreCount) / 10; // Divide circle into 10 segments
            const radius = 200;
            
            return {
                x: 400 + radius * Math.cos(angle),
                y: 300 + radius * Math.sin(angle)
            };
            
        case 'compound':
            // Position compounds around their core
            if (parent) {
                const parentNode = cy.getElementById(parent);
                
                if (parentNode.length > 0) {
                    const parentPos = parentNode.position();
                    const compoundCount = cy.nodes(`[parent="${parent}"][type="compound"]`).length;
                    const angle = (2 * Math.PI * compoundCount) / 12; // Divide circle into 12 segments
                    const radius = 120;
                    
                    return {
                        x: parentPos.x + radius * Math.cos(angle),
                        y: parentPos.y + radius * Math.sin(angle)
                    };
                }
            }
            return { x: Math.random() * 800, y: Math.random() * 600 };
            
        case 'rgroup':
            // Position R-groups near their compound
            if (parent) {
                const parentNode = cy.getElementById(parent);
                
                if (parentNode.length > 0) {
                    const parentPos = parentNode.position();
                    const rgroupCount = cy.nodes(`[parent="${parent}"][type="rgroup"]`).length;
                    const angle = (2 * Math.PI * rgroupCount) / 8; // Divide circle into 8 segments
                    const radius = 80;
                    
                    return {
                        x: parentPos.x + radius * Math.cos(angle),
                        y: parentPos.y + radius * Math.sin(angle)
                    };
                }
            }
            return { x: Math.random() * 800, y: Math.random() * 600 };
            
        default:
            return { x: Math.random() * 800, y: Math.random() * 600 };
    }
}

// Apply network layout
function applyNetworkLayout() {
    const layout = cy.layout({
        name: 'cose',
        idealEdgeLength: 100,
        nodeOverlap: 20,
        refresh: 20,
        fit: true,
        padding: 30,
        randomize: false,
        componentSpacing: 100,
        nodeRepulsion: 400000,
        edgeElasticity: 100,
        nestingFactor: 5,
        gravity: 80,
        numIter: 1000,
        initialTemp: 200,
        coolingFactor: 0.95,
        minTemp: 1.0
    });
    
    layout.run();
}

// Handle node click
function handleNodeClick(node) {
    const data = node.data();
    console.log("Node clicked:", data);
    
    // Toggle node expansion
    toggleNodeExpansion(node);
    
    // Show node details
    showNodeDetails(data);
}

// Toggle node expansion
function toggleNodeExpansion(node) {
    const data = node.data();
    console.log("Toggling expansion for node:", data);
    
    if (!data.has_children) return;
    
    const expanded = !data.expanded;
    node.data('expanded', expanded);
    
    // Find child nodes
    const childNodes = cy.nodes().filter(n => n.data('parent') === data.id);
    
    // Toggle visibility of child nodes and their edges
    childNodes.forEach(childNode => {
        childNode.style('display', expanded ? 'element' : 'none');
        
        // Also toggle edges connected to this child
        cy.edges().filter(e => e.data('source') === childNode.id() || e.data('target') === childNode.id())
            .style('display', expanded ? 'element' : 'none');
    });
    
    // If expanding, also expand any already expanded children
    if (expanded) {
        childNodes.forEach(childNode => {
            if (childNode.data('expanded')) {
                toggleNodeExpansion(childNode);
            }
        });
        
        // Show edges from parent to children
        cy.edges().filter(e => e.data('source') === data.id)
            .style('display', 'element');
    }
}

// Show node details
function showNodeDetails(nodeData) {
    const detailsPanel = $('#node-details');
    
    let content = '';
    
    switch (nodeData.type) {
        case 'core':
            content = `
                <div class="card">
                    <div class="card-header bg-warning">
                        <h5 class="card-title mb-0">${nodeData.name}</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>SMILES:</strong> ${nodeData.smiles}</p>
                        <p><strong>Compounds:</strong> ${nodeData.count}</p>
                        <div class="structure-container" id="structure-${nodeData.id}"></div>
                    </div>
                </div>
            `;
            break;
            
        case 'compound':
            content = `
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">${nodeData.name}</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>SMILES:</strong> ${nodeData.smiles}</p>
                        <p><strong>Activity:</strong> ${nodeData.activity} ${nodeData.activity_unit}</p>
                        <p><strong>pIC50:</strong> ${nodeData.pIC50?.toFixed(2) || 'N/A'}</p>
                        <div class="structure-container" id="structure-${nodeData.id}"></div>
                    </div>
                </div>
            `;
            break;
            
        case 'rgroup':
            content = `
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">${nodeData.name}</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>SMILES:</strong> ${nodeData.smiles}</p>
                        <div class="structure-container" id="structure-${nodeData.id}"></div>
                    </div>
                </div>
            `;
            break;
    }
    
    detailsPanel.html(content);
    
    // Render molecule structure
    if (nodeData.smiles) {
        renderMolecule(nodeData.smiles, nodeData.svg, `structure-${nodeData.id}`);
    }
    
    // Show details panel
    detailsPanel.removeClass('d-none');
}

// Show molecule tooltip
function showMoleculeTooltip(node) {
    const data = node.data();
    console.log("Showing tooltip for node:", data);
    
    // Create tooltip if it doesn't exist
    if ($('#molecule-tooltip').length === 0) {
        $('body').append('<div id="molecule-tooltip" class="d-none"></div>');
    }
    
    // Position tooltip near the node
    const position = node.renderedPosition();
    const tooltip = $('#molecule-tooltip');
    
    tooltip.css({
        'left': position.x + 50,
        'top': position.y - 100
    }).removeClass('d-none');
    
    // Set tooltip content based on node type
    let content = '';
    
    switch (data.type) {
        case 'core':
            content = `
                <div class="tooltip-header bg-warning">Core: ${data.name}</div>
                <div class="tooltip-content">
                    <p>SMILES: ${data.smiles}</p>
                    <div class="tooltip-structure" id="tooltip-structure"></div>
                </div>
            `;
            break;
            
        case 'compound':
            content = `
                <div class="tooltip-header bg-success text-white">Compound: ${data.name}</div>
                <div class="tooltip-content">
                    <p>SMILES: ${data.smiles}</p>
                    <p>Activity: ${data.activity} ${data.activity_unit}</p>
                    <div class="tooltip-structure" id="tooltip-structure"></div>
                </div>
            `;
            break;
            
        case 'rgroup':
            content = `
                <div class="tooltip-header bg-info text-white">R-Group: ${data.name}</div>
                <div class="tooltip-content">
                    <p>SMILES: ${data.smiles}</p>
                    <div class="tooltip-structure" id="tooltip-structure"></div>
                </div>
            `;
            break;
    }
    
    tooltip.html(content);
    
    // Render molecule structure if available
    if (data.smiles) {
        renderMolecule(data.smiles, data.svg, 'tooltip-structure');
    }
}

// Hide molecule tooltip
function hideMoleculeTooltip() {
    $('#molecule-tooltip').addClass('d-none');
}

// Render molecule structure
function renderMolecule(smiles, molblock, containerId) {
    if (!smiles) return;
    
    try {
        // Use RDKit.js if available
        if (typeof RDKit !== 'undefined') {
            const mol = RDKit.Molecule.fromSmiles(smiles);
            const svg = mol.toSVG();
            $(`#${containerId}`).html(svg);
        } 
        // Fallback to molblock if available
        else if (molblock) {
            // Convert molblock to SVG using SmilesDrawer or other library
            if (typeof SmilesDrawer !== 'undefined') {
                const drawer = new SmilesDrawer.Drawer({ width: 300, height: 200 });
                SmilesDrawer.parse(smiles, function(tree) {
                    drawer.draw(tree, containerId, 'light', false);
                });
            } else {
                $(`#${containerId}`).html(`<pre>${molblock}</pre>`);
            }
        }
        // Fallback to just showing SMILES
        else {
            $(`#${containerId}`).html(`<p>${smiles}</p>`);
        }
    } catch (e) {
        console.error("Error rendering molecule:", e);
        $(`#${containerId}`).html(`<p>Error rendering structure</p>`);
    }
}

// Update visualization info
function updateVisualizationInfo(data) {
    const infoPanel = $('#visualization-info');
    
    const content = `
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">Visualization Info</h5>
            </div>
            <div class="card-body">
                <p><strong>Cores:</strong> ${data.core_count || 'N/A'}</p>
                <p><strong>Compounds:</strong> ${data.compound_count || 'N/A'}</p>
                <p><strong>Nodes:</strong> ${data.node_count || 'N/A'}</p>
                <p><strong>Edges:</strong> ${data.edge_count || 'N/A'}</p>
            </div>
        </div>
    `;
    
    infoPanel.html(content).removeClass('d-none');
}

// Create core-based visualization
function createCoreVisualization(dataId) {
    console.log("Creating core-based visualization for data:", dataId);
    
    // Show loading indicator
    $('#loading-indicator').removeClass('d-none');
    
    // Create visualization
    $.ajax({
        url: '/api/visualization_mcs/core-network',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ data_id: dataId }),
        success: function(response) {
            if (response.success) {
                console.log("Core visualization created:", response);
                
                // Load the visualization
                loadCoreVisualization(response.data_id);
                
                // Hide loading indicator
                $('#loading-indicator').addClass('d-none');
            } else {
                console.error("Error creating visualization:", response.error);
                $('#error-message').text(`Error: ${response.error}`).removeClass('d-none');
                $('#loading-indicator').addClass('d-none');
            }
        },
        error: function(xhr) {
            console.error("AJAX error:", xhr.responseText);
            $('#error-message').text(`Error: ${xhr.responseText || 'Failed to create visualization'}`).removeClass('d-none');
            $('#loading-indicator').addClass('d-none');
        }
    });
}

// Function to be called from upload.js after successful upload
function visualizeCoreData(dataId) {
    console.log("Visualizing core data:", dataId);
    localStorage.setItem('currentDataId', dataId);
    createCoreVisualization(dataId);
}

// Export functions
window.visualizeCoreData = visualizeCoreData;
window.loadCoreVisualization = loadCoreVisualization;
