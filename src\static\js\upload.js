// Upload functionality
$(document).ready(function() {
    console.log("Upload.js loaded");
    
    // Handle file upload form submission
    $('#upload-form').on('submit', function(e) {
        e.preventDefault();
        console.log("Upload form submitted");
        
        const fileInput = $('#file')[0];
        const activityType = $('#activity-type').val();
        
        if (!fileInput.files.length) {
            $('#upload-result').html('<div class="alert alert-danger">Please select a file to upload.</div>');
            return;
        }
        
        const file = fileInput.files[0];
        const formData = new FormData();
        formData.append('file', file);
        formData.append('activity_type', activityType);
        
        // Show progress bar
        $('#upload-progress').removeClass('d-none');
        $('#upload-progress .progress-bar').css('width', '0%');
        
        $.ajax({
            url: '/api/upload/upload',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhr: function() {
                const xhr = new XMLHttpRequest();
                xhr.upload.addEventListener('progress', function(e) {
                    if (e.lengthComputable) {
                        const percent = Math.round((e.loaded / e.total) * 100);
                        $('#upload-progress .progress-bar').css('width', percent + '%');
                    }
                });
                return xhr;
            },
            success: function(response) {
                console.log("Upload success:", response);
                $('#upload-progress').addClass('d-none');
                
                if (response.success) {
                    $('#upload-result').html(`
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> File uploaded successfully!
                            <ul class="mb-0">
                                <li>Compounds processed: ${response.compound_count}</li>
                                <li>Activity type: ${response.activity_type}</li>
                            </ul>
                        </div>
                    `);

                    // Store data ID for later use
                    localStorage.setItem('currentDataId', response.data_id);

                    // Show processing options
                    $('#processing-options').removeClass('d-none');
                } else {
                    $('#upload-result').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> Upload failed: ${response.error}
                            ${response.details ? '<ul>' + response.details.map(d => `<li>${d}</li>`).join('') + '</ul>' : ''}
                        </div>
                    `);
                }
            },
            error: function(xhr, status, error) {
                console.error("Upload error:", error);
                $('#upload-progress').addClass('d-none');
                
                let errorMessage = 'An error occurred during upload.';
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.error) {
                        errorMessage = response.error;
                    }
                } catch (e) {
                    // Use default error message
                }
                
                $('#upload-result').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> ${errorMessage}
                    </div>
                `);
            }
        });
    });
    
    // Handle process data button
    $('#process-data').on('click', function() {
        const dataId = localStorage.getItem('currentDataId');
        const threshold = $('#activity-threshold').val();
        
        if (!dataId) {
            $('#upload-result').html('<div class="alert alert-danger">No data available for processing.</div>');
            return;
        }
        
        // Switch to visualization section
        $('.section').removeClass('active');
        $('#visualization-section').addClass('active');

        // Update active state in nav
        $('.nav-link').removeClass('active');
        $('#nav-visualization').addClass('active');

        // Trigger visualization with the current data
        if (typeof createVisualization === 'function') {
            createVisualization(dataId, threshold);
        } else {
            console.error("Visualization function not available");
        }
    });
});
