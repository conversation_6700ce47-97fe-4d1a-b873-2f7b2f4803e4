import os
import json
import logging
from flask import Blueprint, request, jsonify
from rdkit import Chem
from src.routes.mcs import generate_core_network, identify_cores_and_rgroups

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create blueprint
visualization_mcs_bp = Blueprint('visualization_mcs', __name__)

# Data folder
DATA_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads')

@visualization_mcs_bp.route('/core-network', methods=['POST'])
def create_core_network():
    """Create network visualization with core structures and R-groups"""
    data_id = request.json.get('data_id')
    
    if not data_id:
        return jsonify({'success': False, 'error': 'Data ID is required'}), 400
    
    # Check if data exists
    data_path = os.path.join(DATA_FOLDER, f"{data_id}.json")
    if not os.path.exists(data_path):
        return jsonify({'success': False, 'error': 'Data not found'}), 404
    
    try:
        # Load the data
        with open(data_path, 'r') as f:
            data = json.load(f)
        
        compounds = data.get('compounds', [])
        
        if not compounds:
            return jsonify({'success': False, 'error': 'No compounds found in data'}), 400
        
        # Generate core network
        network = generate_core_network(compounds)
        
        # Save visualization data
        viz_id = f"{data_id}_core_viz"
        viz_path = os.path.join(DATA_FOLDER, f"{viz_id}.json")
        
        with open(viz_path, 'w') as f:
            json.dump({
                'visualization': network,
                'original_data_id': data_id,
                'compound_count': len(compounds),
                'core_count': len(network['cores'])
            }, f)
        
        return jsonify({
            'success': True,
            'message': f'Created core network visualization with {len(network["nodes"])} nodes and {len(network["edges"])} edges',
            'data_id': viz_id,
            'node_count': len(network['nodes']),
            'edge_count': len(network['edges']),
            'core_count': len(network['cores'])
        })
        
    except Exception as e:
        logger.error(f"Core visualization error: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@visualization_mcs_bp.route('/core-details/<viz_id>', methods=['GET'])
def get_core_details(viz_id):
    """Get detailed information about cores in a visualization"""
    viz_path = os.path.join(DATA_FOLDER, f"{viz_id}.json")
    
    if not os.path.exists(viz_path):
        return jsonify({'success': False, 'error': 'Visualization not found'}), 404
    
    try:
        with open(viz_path, 'r') as f:
            data = json.load(f)
        
        visualization = data.get('visualization', {})
        cores = visualization.get('cores', [])
        
        return jsonify({
            'success': True,
            'cores': cores
        })
    except Exception as e:
        logger.error(f"Error retrieving core details: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@visualization_mcs_bp.route('/compound-rgroups/<viz_id>/<compound_id>', methods=['GET'])
def get_compound_rgroups(viz_id, compound_id):
    """Get R-groups for a specific compound in a visualization"""
    viz_path = os.path.join(DATA_FOLDER, f"{viz_id}.json")
    
    if not os.path.exists(viz_path):
        return jsonify({'success': False, 'error': 'Visualization not found'}), 404
    
    try:
        with open(viz_path, 'r') as f:
            data = json.load(f)
        
        visualization = data.get('visualization', {})
        nodes = visualization.get('nodes', [])
        
        # Find compound node
        compound_node = next((node for node in nodes if node.get('id') == compound_id), None)
        if not compound_node:
            return jsonify({'success': False, 'error': 'Compound not found'}), 404
        
        # Find R-group nodes
        rgroup_nodes = [node for node in nodes if node.get('type') == 'rgroup' and node.get('parent') == compound_id]
        
        return jsonify({
            'success': True,
            'compound': compound_node,
            'rgroups': rgroup_nodes
        })
    except Exception as e:
        logger.error(f"Error retrieving compound R-groups: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500
