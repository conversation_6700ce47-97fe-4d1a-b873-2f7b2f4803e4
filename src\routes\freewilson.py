from flask import Blueprint, request, jsonify
import os
import json
import pandas as pd
import numpy as np
from rdkit import Chem
from rdkit.Chem import AllChem
from rdkit.Chem.Scaffolds import MurckoScaffold
from rdkit.Chem import rdRGroupDecomposition

freewilson_bp = Blueprint('freewilson', __name__)

# Directory to store data
DATA_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads')
os.makedirs(DATA_FOLDER, exist_ok=True)

def identify_common_scaffold(molecules):
    """Identify the most common scaffold among a set of molecules"""
    scaffolds = {}
    
    for mol in molecules:
        try:
            scaffold = MurckoScaffold.GetScaffoldForMol(mol)
            scaffold_smiles = Chem.MolToSmiles(scaffold)
            
            if scaffold_smiles in scaffolds:
                scaffolds[scaffold_smiles].append(mol)
            else:
                scaffolds[scaffold_smiles] = [mol]
        except:
            continue
    
    # Find the most common scaffold
    if not scaffolds:
        return None, None
    
    most_common = max(scaffolds.items(), key=lambda x: len(x[1]))
    scaffold_smiles = most_common[0]
    scaffold_mol = Chem.MolFromSmiles(scaffold_smiles)
    
    return scaffold_mol, scaffold_smiles

def perform_r_group_decomposition(molecules, scaffold_smarts):
    """Perform R-group decomposition using a scaffold SMARTS pattern"""
    scaffold = Chem.MolFromSmarts(scaffold_smarts)
    if not scaffold:
        return None
    
    # Perform R-group decomposition
    groups, _ = rdRGroupDecomposition.RGroupDecompose([scaffold], molecules, asSmiles=True)
    
    return groups

def run_free_wilson_analysis(molecules, activities, scaffold_smarts=None):
    """Run Free Wilson analysis on a set of molecules"""
    # If no scaffold SMARTS provided, identify common scaffold
    if not scaffold_smarts:
        scaffold_mol, scaffold_smiles = identify_common_scaffold(molecules)
        if not scaffold_mol:
            return {
                'success': False,
                'error': 'Could not identify common scaffold'
            }
        scaffold_smarts = Chem.MolToSmarts(scaffold_mol)
    
    # Perform R-group decomposition
    groups = perform_r_group_decomposition(molecules, scaffold_smarts)
    if not groups:
        return {
            'success': False,
            'error': 'R-group decomposition failed'
        }
    
    # Convert R-groups to features
    features = {}
    for i, rgroups in enumerate(groups):
        for rgroup, smiles in rgroups.items():
            if rgroup.startswith('R'):
                if rgroup not in features:
                    features[rgroup] = []
                features[rgroup].append(smiles)
    
    # Create binary feature matrix
    X = pd.DataFrame(index=range(len(molecules)))
    for rgroup, smiles_list in features.items():
        unique_smiles = set(smiles_list)
        for smiles in unique_smiles:
            if smiles:  # Skip empty R-groups
                feature_name = f"{rgroup}_{smiles}"
                X[feature_name] = [1 if s == smiles else 0 for s in smiles_list]
    
    # Add intercept
    X['intercept'] = 1
    
    # Fit linear regression model
    y = np.array(activities)
    try:
        # Use numpy's least squares solver
        coeffs, residuals, rank, s = np.linalg.lstsq(X.values, y, rcond=None)
        
        # Calculate statistics
        y_pred = X.values @ coeffs
        r_squared = 1 - np.sum((y - y_pred)**2) / np.sum((y - np.mean(y))**2)
        rmse = np.sqrt(np.mean((y - y_pred)**2))
        
        # Create results
        results = {
            'success': True,
            'scaffold': scaffold_smarts,
            'coefficients': {col: float(coeff) for col, coeff in zip(X.columns, coeffs)},
            'statistics': {
                'r_squared': float(r_squared),
                'rmse': float(rmse),
                'sample_size': len(molecules)
            },
            'predictions': {
                'actual': [float(val) for val in y],
                'predicted': [float(val) for val in y_pred]
            }
        }
        
        return results
    except Exception as e:
        return {
            'success': False,
            'error': f'Linear regression failed: {str(e)}'
        }

@freewilson_bp.route('/analyze', methods=['POST'])
def analyze():
    """Run Free Wilson analysis on a dataset"""
    data_id = request.json.get('data_id')
    scaffold_smarts = request.json.get('scaffold_smarts')
    
    if not data_id:
        return jsonify({'success': False, 'error': 'Data ID is required'}), 400
    
    # Check if data exists
    data_path = os.path.join(DATA_FOLDER, f"{data_id}.json")
    if not os.path.exists(data_path):
        return jsonify({'success': False, 'error': 'Data not found'}), 404
    
    try:
        # Load the data
        with open(data_path, 'r') as f:
            data = json.load(f)
        
        compounds = data.get('compounds', [])
        
        if not compounds:
            return jsonify({'success': False, 'error': 'No compounds found in data'}), 400
        
        # Extract molecules and activities
        molecules = []
        activities = []
        
        for compound in compounds:
            smiles = compound.get('smiles')
            mol = Chem.MolFromSmiles(smiles)
            
            if mol:
                molecules.append(mol)
                activities.append(compound.get('pIC50', 0))
        
        if not molecules:
            return jsonify({'success': False, 'error': 'No valid molecules found'}), 400
        
        # Run Free Wilson analysis
        results = run_free_wilson_analysis(molecules, activities, scaffold_smarts)
        
        if not results['success']:
            return jsonify(results), 400
        
        # Save results
        results_id = f"{data_id}_fw"
        results_path = os.path.join(DATA_FOLDER, f"{results_id}.json")
        
        with open(results_path, 'w') as f:
            json.dump(results, f)
        
        return jsonify({
            'success': True,
            'message': 'Free Wilson analysis completed successfully',
            'data_id': results_id,
            'r_squared': results['statistics']['r_squared'],
            'rmse': results['statistics']['rmse']
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@freewilson_bp.route('/results/<results_id>', methods=['GET'])
def get_results(results_id):
    """Get Free Wilson analysis results by ID"""
    results_path = os.path.join(DATA_FOLDER, f"{results_id}.json")
    
    if not os.path.exists(results_path):
        return jsonify({'success': False, 'error': 'Results not found'}), 404
    
    try:
        with open(results_path, 'r') as f:
            results = json.load(f)
        
        return jsonify({
            'success': True,
            'results': results
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
