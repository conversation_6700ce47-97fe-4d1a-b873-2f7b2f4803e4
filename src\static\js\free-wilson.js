// Free Wilson Analysis functionality
$(document).ready(function() {
    // Handle Free Wilson analysis form submission
    $('#run-free-wilson').click(function() {
        const dataId = $('#fw-data-source').val();
        if (!dataId) {
            $('#free-wilson-result').html('<div class="alert alert-danger">Please select a data source</div>');
            return;
        }
        
        const scaffoldSmarts = $('#fw-scaffold-smarts').val();
        
        // Show progress
        $('#free-wilson-progress').removeClass('d-none');
        $('#free-wilson-progress .progress-bar').css('width', '0%');
        $('#free-wilson-result').html('');
        
        // Run Free Wilson analysis
        $.ajax({
            url: '/api/freewilson/analyze',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ 
                data_id: dataId,
                scaffold_smarts: scaffoldSmarts || null
            }),
            xhr: function() {
                const xhr = new XMLHttpRequest();
                xhr.upload.addEventListener('progress', function(e) {
                    if (e.lengthComputable) {
                        const percent = Math.round((e.loaded / e.total) * 100);
                        $('#free-wilson-progress .progress-bar').css('width', percent + '%');
                    }
                }, false);
                return xhr;
            },
            success: function(response) {
                $('#free-wilson-progress').addClass('d-none');
                
                if (response.success) {
                    $('#free-wilson-result').html(`
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle"></i> ${response.message}
                            <p>Analyzed ${response.compound_count} compounds</p>
                        </div>
                    `);
                    
                    // Store analysis ID
                    $('#free-wilson-options').data('fwId', response.data_id);
                    
                    // Get visualization data
                    $.ajax({
                        url: `/api/freewilson/visualization/${response.data_id}`,
                        type: 'GET',
                        success: function(vizResponse) {
                            if (vizResponse.success) {
                                // Show visualization
                                $('#free-wilson-visualization').removeClass('d-none');
                                
                                // Render R-group contribution chart
                                renderRGroupChart(vizResponse.visualization);
                                
                                // Render statistics table
                                renderStatsTable(vizResponse.visualization);
                                
                                // Store visualization ID
                                $('#free-wilson-options').data('fwVizId', vizResponse.data_id);
                            } else {
                                $('#free-wilson-result').append(`
                                    <div class="alert alert-warning mt-3">
                                        <i class="bi bi-exclamation-triangle"></i> Visualization failed: ${vizResponse.error}
                                    </div>
                                `);
                            }
                        },
                        error: handleAjaxError
                    });
                } else {
                    $('#free-wilson-result').html(`
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i> ${response.error}
                        </div>
                    `);
                }
            },
            error: handleAjaxError
        });
    });
    
    // Handle view network button click
    $('#view-fw-network').click(function() {
        const fwVizId = $('#free-wilson-options').data('fwVizId');
        if (!fwVizId) {
            alert('No Free Wilson visualization available');
            return;
        }
        
        // Switch to visualization tab and load the Free Wilson visualization
        switchToTab('visualization-tab');
        loadVisualization(fwVizId);
    });
    
    // Helper function to render R-group contribution chart
    function renderRGroupChart(data) {
        const coefficients = data.coefficients;
        
        // Group coefficients by R-group
        const rGroups = {};
        coefficients.forEach(coef => {
            const rg = coef.rgroup;
            if (!rGroups[rg]) {
                rGroups[rg] = [];
            }
            rGroups[rg].push({
                substituent: coef.substituent,
                coefficient: coef.coefficient,
                std_error: coef.std_error
            });
        });
        
        // Create chart HTML
        let chartHtml = '<div class="row">';
        
        Object.keys(rGroups).forEach(rg => {
            const rgCoefs = rGroups[rg].sort((a, b) => b.coefficient - a.coefficient);
            
            chartHtml += `
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            ${rg} Contributions
                        </div>
                        <div class="card-body p-0">
                            <div class="rg-chart" id="chart-${rg.replace('R', 'r')}" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        chartHtml += '</div>';
        
        // Add chart to DOM
        $('#r-group-chart').html(chartHtml);
        
        // Render each chart
        Object.keys(rGroups).forEach(rg => {
            const rgCoefs = rGroups[rg];
            const chartId = `chart-${rg.replace('R', 'r')}`;
            
            // Create chart data
            const labels = rgCoefs.map(c => c.substituent);
            const values = rgCoefs.map(c => c.coefficient);
            const errors = rgCoefs.map(c => c.std_error);
            
            // Create chart colors
            const colors = values.map(v => v >= 0 ? 'rgba(46, 204, 113, 0.7)' : 'rgba(231, 76, 60, 0.7)');
            const borderColors = values.map(v => v >= 0 ? 'rgb(39, 174, 96)' : 'rgb(192, 57, 43)');
            
            // Create chart
            const ctx = document.getElementById(chartId);
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Contribution',
                        data: values,
                        backgroundColor: colors,
                        borderColor: borderColors,
                        borderWidth: 1,
                        errorBars: {
                            show: true,
                            color: 'rgba(0, 0, 0, 0.5)',
                            lineWidth: 2,
                            tipWidth: 6
                        },
                        errors: errors
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: 'Activity Contribution (pIC50)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Substituent'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: `${rg} Group Contributions to Activity`
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.raw;
                                    const error = errors[context.dataIndex];
                                    return `Contribution: ${value.toFixed(2)} ± ${error.toFixed(2)}`;
                                }
                            }
                        }
                    }
                }
            });
        });
    }
    
    // Helper function to render statistics table
    function renderStatsTable(data) {
        const r2 = data.r2;
        const rmse = data.rmse;
        const intercept = data.intercept;
        const sampleSize = data.sample_size;
        
        const tableHtml = `
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead class="table-primary">
                        <tr>
                            <th>Statistic</th>
                            <th>Value</th>
                            <th>Interpretation</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>R²</td>
                            <td>${r2.toFixed(3)}</td>
                            <td>${getR2Interpretation(r2)}</td>
                        </tr>
                        <tr>
                            <td>RMSE</td>
                            <td>${rmse.toFixed(3)}</td>
                            <td>Root Mean Square Error (lower is better)</td>
                        </tr>
                        <tr>
                            <td>Intercept</td>
                            <td>${intercept.toFixed(3)}</td>
                            <td>Base activity of the scaffold</td>
                        </tr>
                        <tr>
                            <td>Sample Size</td>
                            <td>${sampleSize}</td>
                            <td>${getSampleSizeInterpretation(sampleSize)}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="alert alert-info mt-3">
                <i class="bi bi-info-circle"></i> <strong>Free Wilson Analysis Interpretation:</strong>
                <p>The Free Wilson analysis decomposes the activity contributions of different R-groups. Positive values indicate substituents that enhance activity, while negative values indicate activity-reducing substituents.</p>
                <p>The model quality is indicated by R² (${r2.toFixed(3)}), which represents the proportion of variance explained by the model. ${getR2Interpretation(r2)}</p>
            </div>
        `;
        
        $('#stats-table').html(tableHtml);
    }
    
    // Helper function to interpret R² values
    function getR2Interpretation(r2) {
        if (r2 >= 0.8) return 'Excellent fit (> 0.8)';
        if (r2 >= 0.6) return 'Good fit (0.6 - 0.8)';
        if (r2 >= 0.4) return 'Moderate fit (0.4 - 0.6)';
        return 'Poor fit (< 0.4)';
    }
    
    // Helper function to interpret sample size
    function getSampleSizeInterpretation(size) {
        if (size >= 30) return 'Good sample size (≥ 30)';
        if (size >= 15) return 'Moderate sample size (15 - 29)';
        return 'Small sample size (< 15), interpret with caution';
    }
    
    // Helper function to handle AJAX errors
    function handleAjaxError(xhr) {
        $('#free-wilson-progress').addClass('d-none');
        
        try {
            const response = JSON.parse(xhr.responseText);
            $('#free-wilson-result').html(`
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> ${response.error || 'Request failed'}
                </div>
            `);
        } catch (e) {
            $('#free-wilson-result').html(`
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> Request failed: ${xhr.status} ${xhr.statusText}
                </div>
            `);
        }
    }
    
    // Load Chart.js dynamically
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
    document.body.appendChild(script);
});
