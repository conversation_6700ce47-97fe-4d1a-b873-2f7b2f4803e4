/* Main Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #ffffff;
    color: #333333;
}

/* Navbar */
.navbar {
    background-color: #00337f; /* Deep blue header */
    padding: 0.8rem 1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ffffff;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.5rem 1rem;
    transition: color 0.3s;
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link.active {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

/* Cards */
.card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    background-color: #ffffff;
}

.card-header {
    background-color: #00337f; /* Deep blue header */
    color: #ffffff;
    border-radius: 8px 8px 0 0 !important;
    padding: 1rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Buttons */
.btn-primary {
    background-color: #00337f;
    border-color: #00337f;
}

.btn-primary:hover {
    background-color: #002266;
    border-color: #002266;
}

.btn-outline-secondary {
    color: #00337f;
    border-color: #00337f;
}

.btn-outline-secondary:hover {
    background-color: #00337f;
    color: #ffffff;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

/* Forms */
.form-control, .form-select {
    border-radius: 4px;
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
}

.form-control:focus, .form-select:focus {
    border-color: #00337f;
    box-shadow: 0 0 0 0.25rem rgba(0, 51, 127, 0.25);
}

/* Alerts */
.alert {
    border-radius: 4px;
    padding: 1rem;
}

.alert-info {
    background-color: #e6f3ff;
    border-color: #b8daff;
    color: #004085;
}

/* Progress Bar */
.progress {
    height: 0.75rem;
    border-radius: 4px;
}

/* Visualization */
#cy {
    width: 100%;
    height: 600px;
    background-color: #ffffff;
    border-radius: 4px;
}

#sidebar {
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 100%;
    background-color: #f8f9fa;
    border-left: 1px solid #dee2e6;
    overflow-y: auto;
}

/* Molecule Tooltip */
#molecule-tooltip {
    position: absolute;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    max-width: 300px;
}

.tooltip-header {
    font-weight: bold;
    margin-bottom: 5px;
    color: #00337f;
}

.tooltip-structure {
    margin-top: 10px;
    text-align: center;
}

/* Legend */
.legend-item {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
}

/* Tab Content */
.tab-content {
    padding: 20px 0;
}

.tab-pane {
    animation: fadeIn 0.3s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Enhanced Visualization Styles */
.enhanced-popup, .molecular-popup {
    background: white;
    border: 2px solid #007bff;
    border-radius: 12px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
    padding: 0;
    min-width: 500px;
    max-width: 800px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    z-index: 10000;
    backdrop-filter: blur(15px);
    background: rgba(255, 255, 255, 0.98);
}

.popup-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 15px 20px;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.node-type-badge {
    position: absolute;
    top: 10px;
    right: 60px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
}

.node-type-badge.compound {
    background: rgba(40, 167, 69, 0.8);
    color: white;
}

.node-type-badge.core {
    background: rgba(255, 193, 7, 0.8);
    color: black;
}

.node-type-badge.rgroup, .node-type-badge.fragment {
    background: rgba(23, 162, 184, 0.8);
    color: white;
}

.popup-header h5 {
    margin: 0;
    font-weight: 600;
    font-size: 1.2em;
}

.popup-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.popup-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.popup-body {
    padding: 20px;
}

.structure-display, .large-structure-display {
    border: 2px solid #dee2e6;
    border-radius: 10px;
    padding: 15px;
    background: #f8f9fa;
    min-height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
}

.large-structure-display {
    min-height: 300px;
    background: white;
}

.info-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.info-section h6 {
    color: #007bff;
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 14px;
}

.structure-section {
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.structure-section h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
    text-align: center;
}

.smiles-code {
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    word-break: break-all;
}

.activity-value {
    color: #28a745;
    font-weight: bold;
}

.pic50-value {
    color: #007bff;
    font-weight: bold;
}

.compound-count {
    color: #ffc107;
    font-weight: bold;
}

.lipinski-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
}

.lipinski-badge.violations-0 {
    background: #d4edda;
    color: #155724;
}

.lipinski-badge.violations-1 {
    background: #fff3cd;
    color: #856404;
}

.lipinski-badge.violations-2, .lipinski-badge.violations-3, .lipinski-badge.violations-4 {
    background: #f8d7da;
    color: #721c24;
}

.rgroups-list {
    max-height: 150px;
    overflow-y: auto;
}

.rgroup-item {
    padding: 5px 0;
    border-bottom: 1px solid #dee2e6;
    font-size: 12px;
}

.rgroup-item:last-child {
    border-bottom: none;
}

.quick-tooltip {
    animation: fadeInTooltip 0.2s ease-in;
}

@keyframes fadeInTooltip {
    from { opacity: 0; transform: translateY(5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Professional Zoom Controls */
.zoom-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
}

.zoom-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 6px;
    background: #007bff;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.zoom-btn:hover {
    background: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.zoom-btn:active {
    transform: translateY(0);
}

.zoom-level {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 8px;
    text-align: center;
    font-size: 12px;
    font-weight: bold;
    color: #495057;
    border: 1px solid #dee2e6;
}

/* Professional Layout Controls */
.layout-controls {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-top: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #dee2e6;
}

.control-group {
    margin-bottom: 15px;
}

.control-group label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
    color: #495057;
    font-size: 14px;
}

.layout-select {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.layout-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.spacing-slider {
    width: 70%;
    margin-right: 10px;
}

.spacing-value {
    font-weight: bold;
    color: #007bff;
    font-size: 12px;
}

.apply-layout-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    font-size: 14px;
}

.apply-layout-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea080);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

/* Enhanced Cytoscape Container */
#cy {
    position: relative;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 10px;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Professional Node Styles Enhancement */
.cytoscape-container {
    cursor: grab;
}

.cytoscape-container:active {
    cursor: grabbing;
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    border-radius: 10px;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Professional Tooltips */
.professional-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 13px;
    z-index: 10000;
    pointer-events: none;
    max-width: 250px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.professional-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 20px;
    border: 6px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
}

/* Legend Styles */
.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    margin-right: 10px;
    border: 1px solid #dee2e6;
}

/* Professional Enhancements */
.fade-in {
    animation: fadeInSmooth 0.5s ease-in-out;
}

@keyframes fadeInSmooth {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive improvements */
@media (max-width: 992px) {
    .zoom-controls {
        top: 10px;
        right: 10px;
        padding: 8px;
    }

    .zoom-btn {
        width: 35px;
        height: 35px;
        font-size: 12px;
    }

    .controls-panel {
        padding: 15px;
        margin-bottom: 15px;
    }
}

@media (max-width: 768px) {
    #cy {
        height: 500px !important;
    }

    .molecular-popup, .enhanced-popup {
        min-width: 300px;
        max-width: 95vw;
    }

    .zoom-controls {
        flex-direction: row;
        top: auto;
        bottom: 20px;
        right: 20px;
        left: 20px;
        justify-content: center;
    }
}

/* Layout options styling */
.layout-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.layout-option {
    padding: 8px 16px;
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
    font-weight: 500;
}

.layout-option:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-2px);
}

.layout-option.active {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-color: #0056b3;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

/* Controls panel styling */
.controls-panel {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #dee2e6;
}

/* Section transitions */
.section {
    display: none;
}

.section.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    #sidebar {
        position: relative;
        width: 100%;
        height: auto;
        border-left: none;
        border-top: 1px solid #dee2e6;
    }

    .enhanced-popup {
        min-width: 300px;
        max-width: 90vw;
    }

    .layout-options {
        justify-content: center;
    }

    .controls-panel {
        padding: 15px;
    }
}
