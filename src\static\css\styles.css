/* Main Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #ffffff;
    color: #333333;
}

/* Navbar */
.navbar {
    background-color: #00337f; /* Deep blue header */
    padding: 0.8rem 1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ffffff;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.5rem 1rem;
    transition: color 0.3s;
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link.active {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

/* Cards */
.card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    background-color: #ffffff;
}

.card-header {
    background-color: #00337f; /* Deep blue header */
    color: #ffffff;
    border-radius: 8px 8px 0 0 !important;
    padding: 1rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Buttons */
.btn-primary {
    background-color: #00337f;
    border-color: #00337f;
}

.btn-primary:hover {
    background-color: #002266;
    border-color: #002266;
}

.btn-outline-secondary {
    color: #00337f;
    border-color: #00337f;
}

.btn-outline-secondary:hover {
    background-color: #00337f;
    color: #ffffff;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

/* Forms */
.form-control, .form-select {
    border-radius: 4px;
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
}

.form-control:focus, .form-select:focus {
    border-color: #00337f;
    box-shadow: 0 0 0 0.25rem rgba(0, 51, 127, 0.25);
}

/* Alerts */
.alert {
    border-radius: 4px;
    padding: 1rem;
}

.alert-info {
    background-color: #e6f3ff;
    border-color: #b8daff;
    color: #004085;
}

/* Progress Bar */
.progress {
    height: 0.75rem;
    border-radius: 4px;
}

/* Visualization */
#cy {
    width: 100%;
    height: 600px;
    background-color: #ffffff;
    border-radius: 4px;
}

#sidebar {
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 100%;
    background-color: #f8f9fa;
    border-left: 1px solid #dee2e6;
    overflow-y: auto;
}

/* Molecule Tooltip */
#molecule-tooltip {
    position: absolute;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    max-width: 300px;
}

.tooltip-header {
    font-weight: bold;
    margin-bottom: 5px;
    color: #00337f;
}

.tooltip-structure {
    margin-top: 10px;
    text-align: center;
}

/* Legend */
.legend-item {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
}

/* Tab Content */
.tab-content {
    padding: 20px 0;
}

.tab-pane {
    animation: fadeIn 0.3s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Enhanced Visualization Styles */
.enhanced-popup {
    background: white;
    border: 2px solid #007bff;
    border-radius: 10px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    padding: 0;
    min-width: 400px;
    max-width: 600px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    z-index: 10000;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.popup-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.popup-header h5 {
    margin: 0;
    font-weight: 600;
    font-size: 1.2em;
}

.popup-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.popup-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.popup-body {
    padding: 20px;
}

.structure-display {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    background: #f8f9fa;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Layout options styling */
.layout-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.layout-option {
    padding: 8px 16px;
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
    font-weight: 500;
}

.layout-option:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-2px);
}

.layout-option.active {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-color: #0056b3;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

/* Controls panel styling */
.controls-panel {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #dee2e6;
}

/* Section transitions */
.section {
    display: none;
}

.section.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    #sidebar {
        position: relative;
        width: 100%;
        height: auto;
        border-left: none;
        border-top: 1px solid #dee2e6;
    }

    .enhanced-popup {
        min-width: 300px;
        max-width: 90vw;
    }

    .layout-options {
        justify-content: center;
    }

    .controls-panel {
        padding: 15px;
    }
}
